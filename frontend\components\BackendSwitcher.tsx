import React, { useState } from 'react';
import { API_CONFIG } from '../services/apiConfig';

interface BackendSwitcherProps {
  onBackendChange?: (backend: 'NODEJS_BACKEND' | 'FASTAPI_BACKEND') => void;
}

const BackendSwitcher: React.FC<BackendSwitcherProps> = ({ onBackendChange }) => {
  const [currentBackend, setCurrentBackend] = useState<'NODEJS_BACKEND' | 'FASTAPI_BACKEND'>(
    API_CONFIG.CURRENT_BACKEND
  );

  const handleBackendChange = (backend: 'NODEJS_BACKEND' | 'FASTAPI_BACKEND') => {
    setCurrentBackend(backend);
    API_CONFIG.switchBackend(backend);
    if (onBackendChange) {
      onBackendChange(backend);
    }
    // Reload the page to apply the new backend
    window.location.reload();
  };

  return (
    <div className="flex items-center space-x-3 bg-white p-3 rounded-lg shadow-sm border">
      <span className="text-sm font-medium text-gray-700">Backend:</span>
      <div className="flex space-x-2">
        <button
          onClick={() => handleBackendChange('NODEJS_BACKEND')}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            currentBackend === 'NODEJS_BACKEND'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Node.js (Port 3001)
        </button>
        <button
          onClick={() => handleBackendChange('FASTAPI_BACKEND')}
          className={`px-3 py-1 text-xs rounded-md transition-colors ${
            currentBackend === 'FASTAPI_BACKEND'
              ? 'bg-green-500 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          FastAPI (Port 8000)
        </button>
      </div>
      <div className="text-xs text-gray-500">
        Current: {API_CONFIG.baseUrl}
      </div>
    </div>
  );
};

export default BackendSwitcher;
