
export interface Visit {
  id: string;
  patient: {
    id: string;
    age: number;
    gender: 'Male' | 'Female' | 'Other';
  };
  doctor: {
    id: string;
    name: string;
  };
  date: string; // ISO string
  chiefComplaint: string;
  diagnosis: string;
  medications: string[];
  investigations: string[];
  referred: boolean;
}

export interface Doctor {
  id: string;
  name: string;
}

export interface SymptomCluster {
    clusterName: string;
    symptoms: string[];
    probableDiagnosis: string;
}
