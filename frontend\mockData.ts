import { Visit, Doctor } from './types';

export const doctors: Doctor[] = [
  { id: 'doc1', name: 'Dr. <PERSON>' },
  { id: 'doc2', name: 'Dr. <PERSON>' },
  { id: 'doc3', name: 'Dr. <PERSON>' },
  { id: 'doc4', name: 'Dr. <PERSON>' },
  { id: 'doc5', name: 'Dr. <PERSON>' },
  { id: 'doc6', name: 'Dr. <PERSON>' },
  { id: 'doc7', name: 'Dr. <PERSON>' },
  { id: 'doc8', name: 'Dr. <PERSON>' },
];

const complaints = [
  'Cough', 'Fever', 'Headache', 'Abdominal Pain', 'Fatigue', 'Dizziness',
  'Shortness of breath', 'Chest pain', 'Nausea', 'Back pain', 'Joint pain',
  'Sore throat', 'Runny nose', 'Skin rash', 'Insomnia', 'Anxiety',
  'Depression', 'Weight loss', 'Weight gain', 'Blurred vision', 'Ear pain',
  'Muscle weakness', 'Palpitations', 'Constipation', 'Diarrhea', 'Vomiting',
  'Leg swelling', 'Frequent urination', 'Burning urination', 'Memory problems'
];

const diagnoses = [
  'URTI', 'Hypertension', 'Diabetes Type 2', 'Migraine', 'Anemia',
  'Gastroenteritis', 'Bronchitis', 'Allergic Rhinitis', 'Pneumonia',
  'Asthma', 'COPD', 'Anxiety Disorder', 'Depression', 'Osteoarthritis',
  'Rheumatoid Arthritis', 'Hypothyroidism', 'Hyperthyroidism', 'UTI',
  'Kidney Stones', 'IBS', 'GERD', 'Atrial Fibrillation', 'Heart Failure',
  'Stroke', 'Epilepsy', 'Parkinson Disease', 'Alzheimer Disease',
  'Chronic Kidney Disease', 'Liver Cirrhosis', 'Hepatitis B'
];

const medications = [
  'Paracetamol', 'Amoxicillin', 'Lisinopril', 'Metformin', 'Sumatriptan',
  'Ferrous Sulfate', 'Atorvastatin', 'Omeprazole', 'Aspirin', 'Ibuprofen',
  'Amlodipine', 'Losartan', 'Simvastatin', 'Levothyroxine', 'Albuterol',
  'Prednisone', 'Ciprofloxacin', 'Azithromycin', 'Furosemide', 'Warfarin',
  'Insulin', 'Gabapentin', 'Sertraline', 'Fluoxetine', 'Lorazepam',
  'Hydrochlorothiazide', 'Pantoprazole', 'Ranitidine', 'Cetirizine', 'Montelukast'
];

const investigations = [
  'CBC', 'Chest X-ray', 'Blood Sugar', 'ECG', 'Echocardiogram', 'CT Scan',
  'MRI', 'Ultrasound', 'Urinalysis', 'Liver Function Tests', 'Kidney Function Tests',
  'Thyroid Function Tests', 'Lipid Profile', 'HbA1c', 'ESR', 'CRP',
  'Troponin', 'D-Dimer', 'PT/INR', 'Electrolytes', 'Arterial Blood Gas',
  'Spirometry', 'Stress Test', 'Colonoscopy', 'Endoscopy', 'Mammography',
  'Bone Density Scan', 'PSA', 'Pap Smear', 'Biopsy'
];

const generateRandomDate = (start: Date, end: Date): string => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString();
};

// Helper function to get related complaints for specific diagnoses
const getRelatedComplaint = (diagnosis: string): string => {
  const diagnosisComplaintMap: { [key: string]: string[] } = {
    'URTI': ['Cough', 'Fever', 'Sore throat', 'Runny nose'],
    'Bronchitis': ['Cough', 'Fever', 'Shortness of breath', 'Chest pain'],
    'Pneumonia': ['Cough', 'Fever', 'Shortness of breath', 'Chest pain'],
    'Asthma': ['Shortness of breath', 'Cough', 'Chest pain'],
    'COPD': ['Shortness of breath', 'Cough', 'Fatigue'],
    'Hypertension': ['Headache', 'Dizziness', 'Fatigue', 'Blurred vision'],
    'Migraine': ['Headache', 'Nausea', 'Blurred vision'],
    'Gastroenteritis': ['Abdominal Pain', 'Nausea', 'Vomiting', 'Diarrhea'],
    'IBS': ['Abdominal Pain', 'Constipation', 'Diarrhea'],
    'GERD': ['Chest pain', 'Nausea', 'Abdominal Pain'],
    'Diabetes Type 2': ['Fatigue', 'Frequent urination', 'Blurred vision', 'Weight loss'],
    'UTI': ['Burning urination', 'Frequent urination', 'Abdominal Pain'],
    'Anxiety Disorder': ['Anxiety', 'Palpitations', 'Insomnia', 'Fatigue'],
    'Depression': ['Depression', 'Fatigue', 'Insomnia', 'Weight loss'],
    'Osteoarthritis': ['Joint pain', 'Back pain', 'Muscle weakness'],
    'Rheumatoid Arthritis': ['Joint pain', 'Fatigue', 'Muscle weakness'],
    'Heart Failure': ['Shortness of breath', 'Leg swelling', 'Fatigue', 'Palpitations'],
    'Atrial Fibrillation': ['Palpitations', 'Dizziness', 'Shortness of breath'],
  };

  const possibleComplaints = diagnosisComplaintMap[diagnosis];
  if (possibleComplaints) {
    return possibleComplaints[Math.floor(Math.random() * possibleComplaints.length)];
  }
  return complaints[Math.floor(Math.random() * complaints.length)];
};

// Helper function to get appropriate medications for diagnosis
const getMedicationsForDiagnosis = (diagnosis: string): string[] => {
  const diagnosisMedicationMap: { [key: string]: string[] } = {
    'URTI': ['Paracetamol', 'Amoxicillin', 'Cetirizine'],
    'Bronchitis': ['Amoxicillin', 'Albuterol', 'Prednisone'],
    'Pneumonia': ['Amoxicillin', 'Azithromycin', 'Paracetamol'],
    'Asthma': ['Albuterol', 'Prednisone', 'Montelukast'],
    'COPD': ['Albuterol', 'Prednisone', 'Furosemide'],
    'Hypertension': ['Lisinopril', 'Amlodipine', 'Hydrochlorothiazide'],
    'Diabetes Type 2': ['Metformin', 'Insulin', 'Lisinopril'],
    'Migraine': ['Sumatriptan', 'Paracetamol', 'Ibuprofen'],
    'Gastroenteritis': ['Paracetamol', 'Omeprazole', 'Ciprofloxacin'],
    'UTI': ['Ciprofloxacin', 'Azithromycin'],
    'Anxiety Disorder': ['Sertraline', 'Lorazepam'],
    'Depression': ['Sertraline', 'Fluoxetine'],
    'Osteoarthritis': ['Ibuprofen', 'Paracetamol', 'Gabapentin'],
    'Heart Failure': ['Furosemide', 'Lisinopril', 'Atorvastatin'],
    'Atrial Fibrillation': ['Warfarin', 'Furosemide', 'Lisinopril'],
  };

  const possibleMeds = diagnosisMedicationMap[diagnosis] || medications;
  const numMeds = Math.floor(Math.random() * 4) + 1;
  return possibleMeds.slice(0, numMeds);
};

// Helper function to get appropriate investigations for diagnosis
const getInvestigationsForDiagnosis = (diagnosis: string): string[] => {
  const diagnosisInvestigationMap: { [key: string]: string[] } = {
    'URTI': ['CBC', 'Chest X-ray'],
    'Bronchitis': ['Chest X-ray', 'CBC', 'Spirometry'],
    'Pneumonia': ['Chest X-ray', 'CBC', 'Arterial Blood Gas'],
    'Asthma': ['Spirometry', 'Chest X-ray'],
    'COPD': ['Spirometry', 'Chest X-ray', 'Arterial Blood Gas'],
    'Hypertension': ['ECG', 'Echocardiogram', 'Kidney Function Tests'],
    'Diabetes Type 2': ['Blood Sugar', 'HbA1c', 'Kidney Function Tests'],
    'Migraine': ['CT Scan', 'MRI'],
    'Gastroenteritis': ['CBC', 'Electrolytes'],
    'UTI': ['Urinalysis', 'Kidney Function Tests'],
    'Anxiety Disorder': ['ECG', 'Thyroid Function Tests'],
    'Depression': ['Thyroid Function Tests', 'CBC'],
    'Heart Failure': ['Echocardiogram', 'ECG', 'Chest X-ray'],
    'Atrial Fibrillation': ['ECG', 'Echocardiogram', 'Thyroid Function Tests'],
  };

  const possibleInvestigations = diagnosisInvestigationMap[diagnosis] || investigations;
  const numInvestigations = Math.floor(Math.random() * 3) + 1;
  return possibleInvestigations.slice(0, numInvestigations);
};

export const visits: Visit[] = Array.from({ length: 1000 }, (_, i) => {
  const doctor = doctors[Math.floor(Math.random() * doctors.length)];
  const diagnosis = diagnoses[Math.floor(Math.random() * diagnoses.length)];

  // Age distribution that's more realistic for different conditions
  let age: number;
  if (['Diabetes Type 2', 'Hypertension', 'Heart Failure', 'COPD'].includes(diagnosis)) {
    age = Math.floor(Math.random() * 40) + 40; // 40-80 for chronic conditions
  } else if (['URTI', 'Gastroenteritis', 'Bronchitis'].includes(diagnosis)) {
    age = Math.floor(Math.random() * 70) + 10; // 10-80 for common infections
  } else {
    age = Math.floor(Math.random() * 75) + 5; // 5-80 general
  }

  const gender = Math.random() > 0.52 ? 'Female' : 'Male'; // Slightly more females as per demographics
  const relatedComplaint = getRelatedComplaint(diagnosis);

  // Some patients might have multiple visits
  const patientId = Math.floor(Math.random() * 800) + 1; // 800 unique patients for 1000 visits

  return {
    id: `visit${i + 1}`,
    patient: {
      id: `pat${patientId}`,
      age,
      gender,
    },
    doctor,
    date: generateRandomDate(new Date(2023, 0, 1), new Date(2024, 11, 31)),
    chiefComplaint: relatedComplaint,
    diagnosis,
    medications: getMedicationsForDiagnosis(diagnosis),
    investigations: getInvestigationsForDiagnosis(diagnosis),
    referred: Math.random() > 0.88, // 12% referral rate
  };
});

export const chiefComplaintsForAI: string[] = Array.from(new Set(visits.map(v => v.chiefComplaint).slice(0, 50)));

// Additional mock data for enhanced functionality
export const symptomClusters = [
  {
    clusterName: "Respiratory Symptoms",
    symptoms: ["Cough", "Shortness of breath", "Chest pain", "Fever"],
    probableDiagnosis: "URTI, Bronchitis, Pneumonia"
  },
  {
    clusterName: "Cardiovascular Symptoms",
    symptoms: ["Chest pain", "Palpitations", "Leg swelling", "Dizziness"],
    probableDiagnosis: "Heart Failure, Atrial Fibrillation, Hypertension"
  },
  {
    clusterName: "Gastrointestinal Symptoms",
    symptoms: ["Abdominal Pain", "Nausea", "Vomiting", "Diarrhea"],
    probableDiagnosis: "Gastroenteritis, IBS, GERD"
  },
  {
    clusterName: "Neurological Symptoms",
    symptoms: ["Headache", "Dizziness", "Memory problems", "Blurred vision"],
    probableDiagnosis: "Migraine, Stroke, Hypertension"
  },
  {
    clusterName: "Metabolic Symptoms",
    symptoms: ["Fatigue", "Weight loss", "Frequent urination", "Blurred vision"],
    probableDiagnosis: "Diabetes Type 2, Hyperthyroidism"
  }
];

// Department/Specialty data
export const departments = [
  { id: 'dept1', name: 'Internal Medicine', doctors: ['doc1', 'doc2', 'doc3'] },
  { id: 'dept2', name: 'Cardiology', doctors: ['doc4', 'doc5'] },
  { id: 'dept3', name: 'Pulmonology', doctors: ['doc6'] },
  { id: 'dept4', name: 'Gastroenterology', doctors: ['doc7'] },
  { id: 'dept5', name: 'Neurology', doctors: ['doc8'] },
];

// Patient demographics summary
export const patientDemographics = {
  totalPatients: 800,
  ageGroups: {
    '0-18': Math.floor(800 * 0.15),
    '19-35': Math.floor(800 * 0.25),
    '36-50': Math.floor(800 * 0.25),
    '51-65': Math.floor(800 * 0.20),
    '65+': Math.floor(800 * 0.15)
  },
  genderDistribution: {
    'Male': Math.floor(800 * 0.48),
    'Female': Math.floor(800 * 0.52)
  }
};