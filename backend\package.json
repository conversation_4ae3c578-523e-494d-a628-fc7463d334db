{"name": "ai-clinical-backend", "version": "1.0.0", "description": "Backend API for AI Clinical Dashboard", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "pg": "^8.11.3"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "jest": "^29.7.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["clinical", "api", "healthcare", "analytics"], "author": "AI Clinical Team", "license": "MIT"}