// Simple API test script
const BASE_URL = 'http://localhost:3001/api/clinical';

const endpoints = [
  { name: 'Health Check', url: '/health' },
  { name: 'Doctors', url: '/doctors' },
  { name: 'Clinical Data (5 records)', url: '/data?limit=5' },
  { name: 'Statistics', url: '/statistics' },
  { name: 'Diagnosis Trends', url: '/diagnosis-trends' },
  { name: 'Doctor Activity', url: '/doctor-activity' },
  { name: 'Diagnosis by Age', url: '/diagnosis-by-age' },
  { name: 'Symptom Clusters', url: '/symptom-clusters' },
  { name: 'Patient Demographics', url: '/patient-demographics' },
  { name: 'Quality Metrics', url: '/quality-metrics' }
];

async function testEndpoint(name, url) {
  try {
    console.log(`\n🧪 Testing: ${name}`);
    console.log(`📡 URL: ${BASE_URL}${url}`);
    
    const response = await fetch(`${BASE_URL}${url}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Success (${response.status})`);
      if (data.data) {
        if (Array.isArray(data.data)) {
          console.log(`📊 Returned ${data.data.length} items`);
        } else if (typeof data.data === 'object') {
          console.log(`📊 Returned object with keys: ${Object.keys(data.data).join(', ')}`);
        }
      }
    } else {
      console.log(`❌ Failed (${response.status})`);
      console.log(`💬 Error: ${data.error?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`💥 Network Error: ${error.message}`);
  }
}

async function runAllTests() {
  console.log('🚀 Starting API Tests...\n');
  console.log('=' .repeat(50));
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint.name, endpoint.url);
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 All tests completed!');
}

// Run if called directly
if (typeof window === 'undefined') {
  runAllTests().catch(console.error);
}

export { runAllTests, testEndpoint };
