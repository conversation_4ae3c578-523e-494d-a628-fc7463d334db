import { ClinicalModel } from '../models/clinical.js';
import { SymptomCluster, Visit } from '../types/clinical.js';
import { logger } from '../utils/logger.js';

export class AnalyticsService {
  
  static async getSymptomClusters(): Promise<SymptomCluster[]> {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({ limit: 10000 });
      
      // Group symptoms by diagnosis
      const diagnosisSymptomMap: Record<string, Set<string>> = {};
      
      visits.forEach(visit => {
        if (!diagnosisSymptomMap[visit.diagnosis]) {
          diagnosisSymptomMap[visit.diagnosis] = new Set();
        }
        diagnosisSymptomMap[visit.diagnosis].add(visit.chiefComplaint);
      });

      // Create symptom clusters based on common patterns
      const clusters: SymptomCluster[] = [];
      
      // Respiratory cluster
      const respiratorySymptoms = new Set<string>();
      const respiratoryDiagnoses = new Set<string>();
      
      visits.forEach(visit => {
        const complaint = visit.chiefComplaint.toLowerCase();
        if (complaint.includes('cough') || complaint.includes('breath') || 
            complaint.includes('chest') || complaint.includes('fever')) {
          respiratorySymptoms.add(visit.chiefComplaint);
          if (visit.diagnosis.toLowerCase().includes('urti') || 
              visit.diagnosis.toLowerCase().includes('bronchitis') ||
              visit.diagnosis.toLowerCase().includes('pneumonia') ||
              visit.diagnosis.toLowerCase().includes('asthma')) {
            respiratoryDiagnoses.add(visit.diagnosis);
          }
        }
      });

      if (respiratorySymptoms.size > 0) {
        clusters.push({
          clusterName: "Respiratory Symptoms",
          symptoms: Array.from(respiratorySymptoms).slice(0, 10),
          probableDiagnosis: Array.from(respiratoryDiagnoses).slice(0, 5).join(', ')
        });
      }

      // Cardiovascular cluster
      const cardioSymptoms = new Set<string>();
      const cardioDiagnoses = new Set<string>();
      
      visits.forEach(visit => {
        const complaint = visit.chiefComplaint.toLowerCase();
        if (complaint.includes('chest') || complaint.includes('palpitation') || 
            complaint.includes('swelling') || complaint.includes('dizz')) {
          cardioSymptoms.add(visit.chiefComplaint);
          if (visit.diagnosis.toLowerCase().includes('heart') || 
              visit.diagnosis.toLowerCase().includes('hypertension') ||
              visit.diagnosis.toLowerCase().includes('atrial')) {
            cardioDiagnoses.add(visit.diagnosis);
          }
        }
      });

      if (cardioSymptoms.size > 0) {
        clusters.push({
          clusterName: "Cardiovascular Symptoms",
          symptoms: Array.from(cardioSymptoms).slice(0, 10),
          probableDiagnosis: Array.from(cardioDiagnoses).slice(0, 5).join(', ')
        });
      }

      // Gastrointestinal cluster
      const giSymptoms = new Set<string>();
      const giDiagnoses = new Set<string>();
      
      visits.forEach(visit => {
        const complaint = visit.chiefComplaint.toLowerCase();
        if (complaint.includes('abdominal') || complaint.includes('nausea') || 
            complaint.includes('vomit') || complaint.includes('diarrhea')) {
          giSymptoms.add(visit.chiefComplaint);
          if (visit.diagnosis.toLowerCase().includes('gastro') || 
              visit.diagnosis.toLowerCase().includes('ibs') ||
              visit.diagnosis.toLowerCase().includes('gerd')) {
            giDiagnoses.add(visit.diagnosis);
          }
        }
      });

      if (giSymptoms.size > 0) {
        clusters.push({
          clusterName: "Gastrointestinal Symptoms",
          symptoms: Array.from(giSymptoms).slice(0, 10),
          probableDiagnosis: Array.from(giDiagnoses).slice(0, 5).join(', ')
        });
      }

      // Neurological cluster
      const neuroSymptoms = new Set<string>();
      const neuroDiagnoses = new Set<string>();
      
      visits.forEach(visit => {
        const complaint = visit.chiefComplaint.toLowerCase();
        if (complaint.includes('headache') || complaint.includes('dizz') || 
            complaint.includes('memory') || complaint.includes('vision')) {
          neuroSymptoms.add(visit.chiefComplaint);
          if (visit.diagnosis.toLowerCase().includes('migraine') || 
              visit.diagnosis.toLowerCase().includes('stroke') ||
              visit.diagnosis.toLowerCase().includes('epilepsy')) {
            neuroDiagnoses.add(visit.diagnosis);
          }
        }
      });

      if (neuroSymptoms.size > 0) {
        clusters.push({
          clusterName: "Neurological Symptoms",
          symptoms: Array.from(neuroSymptoms).slice(0, 10),
          probableDiagnosis: Array.from(neuroDiagnoses).slice(0, 5).join(', ')
        });
      }

      // Metabolic cluster
      const metaSymptoms = new Set<string>();
      const metaDiagnoses = new Set<string>();
      
      visits.forEach(visit => {
        const complaint = visit.chiefComplaint.toLowerCase();
        if (complaint.includes('fatigue') || complaint.includes('weight') || 
            complaint.includes('urination') || complaint.includes('thirst')) {
          metaSymptoms.add(visit.chiefComplaint);
          if (visit.diagnosis.toLowerCase().includes('diabetes') || 
              visit.diagnosis.toLowerCase().includes('thyroid') ||
              visit.diagnosis.toLowerCase().includes('metabolic')) {
            metaDiagnoses.add(visit.diagnosis);
          }
        }
      });

      if (metaSymptoms.size > 0) {
        clusters.push({
          clusterName: "Metabolic Symptoms",
          symptoms: Array.from(metaSymptoms).slice(0, 10),
          probableDiagnosis: Array.from(metaDiagnoses).slice(0, 5).join(', ')
        });
      }

      return clusters;
    } catch (error) {
      logger.error('Error generating symptom clusters', { error });
      throw error;
    }
  }

  static async getPatientDemographics() {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({ limit: 10000 });
      
      const uniquePatients = new Map<string, Visit>();
      visits.forEach(visit => {
        if (!uniquePatients.has(visit.patient.id)) {
          uniquePatients.set(visit.patient.id, visit);
        }
      });

      const patients = Array.from(uniquePatients.values());
      const totalPatients = patients.length;

      // Age group distribution
      const ageGroups = {
        '0-18': 0,
        '19-35': 0,
        '36-50': 0,
        '51-65': 0,
        '65+': 0
      };

      patients.forEach(patient => {
        const age = patient.patient.age;
        if (age <= 18) ageGroups['0-18']++;
        else if (age <= 35) ageGroups['19-35']++;
        else if (age <= 50) ageGroups['36-50']++;
        else if (age <= 65) ageGroups['51-65']++;
        else ageGroups['65+']++;
      });

      // Gender distribution
      const genderDistribution = patients.reduce((acc, patient) => {
        acc[patient.patient.gender] = (acc[patient.patient.gender] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalPatients,
        ageGroups,
        genderDistribution
      };
    } catch (error) {
      logger.error('Error getting patient demographics', { error });
      throw error;
    }
  }

  static async getQualityMetrics() {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({ limit: 10000 });
      
      if (visits.length === 0) {
        return {
          totalPatients: 0,
          referralRate: 0,
          polypharmacyCount: 0,
          avgMedicationsPerVisit: 0,
          avgInvestigationsPerVisit: 0,
          mostCommonDiagnosis: 'N/A',
          mostCommonComplaint: 'N/A'
        };
      }

      const totalPatients = new Set(visits.map(v => v.patient.id)).size;
      const referralRate = (visits.filter(v => v.referred).length / visits.length) * 100;
      const polypharmacyCount = visits.filter(v => v.medications.length >= 5).length;
      
      const avgMedicationsPerVisit = visits.reduce((sum, v) => sum + v.medications.length, 0) / visits.length;
      const avgInvestigationsPerVisit = visits.reduce((sum, v) => sum + v.investigations.length, 0) / visits.length;

      // Most common diagnosis
      const diagnosisCounts = visits.reduce((acc, visit) => {
        acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      const mostCommonDiagnosis = Object.entries(diagnosisCounts).sort((a, b) => b[1] - a[1])[0]?.[0] || 'N/A';

      // Most common complaint
      const complaintCounts = visits.reduce((acc, visit) => {
        acc[visit.chiefComplaint] = (acc[visit.chiefComplaint] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      const mostCommonComplaint = Object.entries(complaintCounts).sort((a, b) => b[1] - a[1])[0]?.[0] || 'MANDAR';

      return {
        totalPatients,
        referralRate: Math.round(referralRate * 100) / 100,
        polypharmacyCount,
        avgMedicationsPerVisit: Math.round(avgMedicationsPerVisit * 100) / 100,
        avgInvestigationsPerVisit: Math.round(avgInvestigationsPerVisit * 100) / 100,
        mostCommonDiagnosis,
        mostCommonComplaint
      };
    } catch (error) {
      logger.error('Error getting quality metrics', { error });
      throw error;
    }
  }
}
