import { useMemo } from 'react';
import { Visit, Doctor } from '../types';

export const useClinicalData = (visits: Visit[], doctors: Doctor[]) => {
    const clinicalSummary = useMemo(() => {
        if (visits.length === 0) return null;

        const complaintCounts = visits.reduce((acc, visit) => {
            acc[visit.chiefComplaint] = (acc[visit.chiefComplaint] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        const topComplaint = Object.entries(complaintCounts).sort((a, b) => b[1] - a[1])[0];

        const diagnosisCounts = visits.reduce((acc, visit) => {
            acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        const topDiagnosis = Object.entries(diagnosisCounts).sort((a, b) => b[1] - a[1])[0];

        const medicationCounts = visits.flatMap(v => v.medications).reduce((acc, med) => {
            acc[med] = (acc[med] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        const topMedication = Object.entries(medicationCounts).sort((a, b) => b[1] - a[1])[0];
        
        const avgInvestigations = visits.reduce((sum, v) => sum + v.investigations.length, 0) / visits.length;

        return {
            topComplaint: { name: topComplaint[0], count: topComplaint[1] },
            topDiagnosis: { name: topDiagnosis[0], count: topDiagnosis[1] },
            topMedication: { name: topMedication[0], count: topMedication[1] },
            avgInvestigations: avgInvestigations.toFixed(1),
            mostFrequentPair: `${topComplaint[0]} → ${topDiagnosis[0]}`,
        };
    }, [visits]);

    const diagnosisTrends = useMemo(() => {
        const monthlyData: Record<string, Record<string, number>> = {};
        const top5Diagnoses = Object.entries(visits.reduce((acc, visit) => {
            acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
            return acc;
        }, {} as Record<string, number>)).sort((a, b) => b[1] - a[1]).slice(0, 5).map(d => d[0]);

        visits.forEach(visit => {
            if (top5Diagnoses.includes(visit.diagnosis)) {
                const month = new Date(visit.date).toLocaleString('default', { month: 'short' });
                if (!monthlyData[month]) {
                    monthlyData[month] = {};
                }
                monthlyData[month][visit.diagnosis] = (monthlyData[month][visit.diagnosis] || 0) + 1;
            }
        });

        const sortedMonths = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const labels = sortedMonths.filter(m => monthlyData[m]);
        
        const datasets = top5Diagnoses.map(diagnosis => ({
            label: diagnosis,
            data: labels.map(month => monthlyData[month]?.[diagnosis] || 0),
        }));

        return { labels, datasets };
    }, [visits]);

    const doctorActivity = useMemo(() => {
        return doctors.map(doctor => {
            const docVisits = visits.filter(v => v.doctor.id === doctor.id);
            if (docVisits.length === 0) {
                return { name: doctor.name, avgPatients: 0, topDiagnosis: 'N/A', avgMeds: 0 };
            }
            const diagnosisCounts = docVisits.reduce((acc, v) => {
                acc[v.diagnosis] = (acc[v.diagnosis] || 0) + 1;
                return acc;
            }, {} as Record<string, number>);
            const topDiagnosis = Object.keys(diagnosisCounts).length > 0
                ? Object.entries(diagnosisCounts).sort((a, b) => b[1] - a[1])[0][0]
                : 'N/A';
            const avgMeds = docVisits.reduce((sum, v) => sum + v.medications.length, 0) / docVisits.length;
            
            // Assuming data is for a 365 day period for avg patients/day
            const avgPatients = (docVisits.length / 365).toFixed(1);

            return {
                name: doctor.name,
                avgPatients,
                topDiagnosis,
                avgMeds: avgMeds.toFixed(1),
            };
        });
    }, [visits, doctors]);
    
    const diagnosisByAge = useMemo(() => {
        const ageGroups = { '0-18': [], '19-40': [], '41-65': [], '65+': [] };
        const diagnoses = [...new Set(visits.map(v => v.diagnosis))];
        const data: Record<string, Record<string, number>> = {};

        diagnoses.forEach(d => {
            data[d] = { '0-18': 0, '19-40': 0, '41-65': 0, '65+': 0 };
        });

        visits.forEach(v => {
            let group: keyof typeof ageGroups;
            if (v.patient.age <= 18) group = '0-18';
            else if (v.patient.age <= 40) group = '19-40';
            else if (v.patient.age <= 65) group = '41-65';
            else group = '65+';
            if(data[v.diagnosis]) {
                data[v.diagnosis][group]++;
            }
        });
        
        const top5Diagnoses = Object.entries(visits.reduce((acc, visit) => {
            acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
            return acc;
        }, {} as Record<string, number>)).sort((a, b) => b[1] - a[1]).slice(0, 5).map(d => d[0]);

        const labels = Object.keys(ageGroups);
        const datasets = top5Diagnoses.map(diagnosis => ({
            label: diagnosis,
            data: labels.map(ageGroup => data[diagnosis][ageGroup as keyof typeof ageGroups])
        }));

        return { labels, datasets };

    }, [visits]);

    const qualityMetrics = useMemo(() => {
        if (visits.length === 0) {
            return {
                totalPatients: 0,
                referralRate: 0,
                polypharmacyCount: 0,
            };
        }

        const totalPatients = new Set(visits.map(v => v.patient.id)).size;
        const referralRate = (visits.filter(v => v.referred).length / visits.length) * 100;
        const polypharmacyCount = visits.filter(v => v.medications.length >= 5).length;

        return {
            totalPatients,
            referralRate,
            polypharmacyCount,
        };
    }, [visits]);

    return { clinicalSummary, diagnosisTrends, doctorActivity, diagnosisByAge, qualityMetrics };
};