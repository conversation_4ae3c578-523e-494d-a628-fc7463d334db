
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Clinical Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            colors: {
              'brand-primary': '#007A7A',
              'brand-secondary': '#00A3A3',
              'brand-light': '#E0F2F2',
              'brand-dark': '#004D4D',
              'neutral-light': '#F8F9FA',
              'neutral-medium': '#E9ECEF',
              'neutral-dark': '#343A40',
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1",
    "@google/genai": "https://esm.sh/@google/genai@^1.12.0",
    "recharts": "https://esm.sh/recharts@^3.1.0",
    "chart.js": "https://esm.sh/chart.js@^4.5.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-neutral-light">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
