{"name": "ai-clinical-fastapi-backend", "version": "1.0.0", "description": "FastAPI backend for AI Clinical Dashboard", "scripts": {"dev": "python run.py", "start": "python run.py", "install": "python -m pip install -r requirements.txt", "test": "pytest", "docs": "start http://localhost:8000/docs"}, "keywords": ["<PERSON><PERSON><PERSON>", "clinical", "dashboard", "api"], "author": "AI Clinical Dashboard Team", "license": "MIT"}