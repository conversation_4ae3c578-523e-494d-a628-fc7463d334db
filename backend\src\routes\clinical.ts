import { Router } from 'express';
import { ClinicalController } from '../controllers/clinicalController.js';
import {
  validateQuery,
  sanitizeQuery,
  validateRateLimit,
  validateDateRange,
  clinicalDataQuerySchema,
  statisticsQuerySchema
} from '../utils/validation.js';

const router = Router();

/**
 * @route GET /api/clinical/data
 * @desc Get clinical data with optional filtering and pagination
 * @query page - Page number (default: 1)
 * @query limit - Items per page (default: 100, max: 1000)
 * @query startDate - Filter by start date (ISO string)
 * @query endDate - Filter by end date (ISO string)
 * @query doctorId - Filter by doctor ID
 * @query diagnosis - Filter by diagnosis (partial match)
 * @query ageGroup - Filter by age group (<16, 17-25, 26-40, 41-55, 56-65, >65)
 * @query gender - Filter by gender (Male, Female, Other)
 */
router.get('/data',
  sanitizeQuery,
  validateQuery(clinicalDataQuerySchema),
  validateRateLim<PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ClinicalController.getClinicalData
);

/**
 * @route GET /api/clinical/statistics
 * @desc Get clinical statistics and summary data
 * @query startDate - Filter by start date (ISO string)
 * @query endDate - Filter by end date (ISO string)
 * @query doctorId - Filter by doctor ID
 */
router.get('/statistics',
  sanitizeQuery,
  validateQuery(statisticsQuerySchema),
  validateDateRange,
  ClinicalController.getStatistics
);

/**
 * @route GET /api/clinical/diagnosis-trends
 * @desc Get diagnosis trends over time
 * @query startDate - Filter by start date (ISO string)
 * @query endDate - Filter by end date (ISO string)
 * @query doctorId - Filter by doctor ID
 */
router.get('/diagnosis-trends',
  sanitizeQuery,
  validateQuery(statisticsQuerySchema),
  validateDateRange,
  ClinicalController.getDiagnosisTrends
);

/**
 * @route GET /api/clinical/doctor-activity
 * @desc Get doctor activity and performance metrics
 * @query startDate - Filter by start date (ISO string)
 * @query endDate - Filter by end date (ISO string)
 */
router.get('/doctor-activity',
  sanitizeQuery,
  validateQuery(statisticsQuerySchema),
  validateDateRange,
  ClinicalController.getDoctorActivity
);

/**
 * @route GET /api/clinical/diagnosis-by-age
 * @desc Get diagnosis distribution by age groups
 * @query startDate - Filter by start date (ISO string)
 * @query endDate - Filter by end date (ISO string)
 * @query doctorId - Filter by doctor ID
 */
router.get('/diagnosis-by-age',
  sanitizeQuery,
  validateQuery(statisticsQuerySchema),
  validateDateRange,
  ClinicalController.getDiagnosisByAge
);

/**
 * @route GET /api/clinical/doctors
 * @desc Get list of all doctors
 */
router.get('/doctors', ClinicalController.getDoctors);

/**
 * @route GET /api/clinical/health
 * @desc Health check endpoint for the clinical API
 */
router.get('/health', ClinicalController.getHealthCheck);

/**
 * @route GET /api/clinical/symptom-clusters
 * @desc Get symptom clusters and probable diagnoses
 */
router.get('/symptom-clusters', ClinicalController.getSymptomClusters);

/**
 * @route GET /api/clinical/patient-demographics
 * @desc Get patient demographic information
 */
router.get('/patient-demographics', ClinicalController.getPatientDemographics);

/**
 * @route GET /api/clinical/quality-metrics
 * @desc Get quality metrics and performance indicators
 */
router.get('/quality-metrics', ClinicalController.getQualityMetrics);

export { router as clinicalRoutes };
