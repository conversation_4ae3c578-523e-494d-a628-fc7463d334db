# AI Clinical Dashboard - FastAPI Backend

This is the FastAPI backend for the AI Clinical Dashboard application.

## Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **PostgreSQL Integration**: Direct database connection with psycopg2
- **Pydantic Models**: Type validation and serialization
- **CORS Support**: Cross-origin resource sharing for frontend integration
- **Date & Unit Filtering**: Filter clinical data by date range and unit
- **Comprehensive API**: All endpoints from the Node.js backend

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Configuration**:
   Copy `.env.example` to `.env` and configure your database settings:
   ```
   DB_HOST=************
   DB_PORT=5432
   DB_NAME=analytics
   DB_USER=analyser
   DB_PASSWORD=An@lyser123
   API_PORT=8000
   ```

3. **Run the Server**:
   ```bash
   python run.py
   ```

   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## API Endpoints

### Clinical Data
- `GET /api/clinical/data` - Get clinical data with filtering and pagination
- `GET /api/clinical/statistics` - Get clinical statistics
- `GET /api/clinical/diagnosis-trends` - Get diagnosis trends over time
- `GET /api/clinical/doctor-activity` - Get doctor activity metrics
- `GET /api/clinical/diagnosis-by-age` - Get diagnosis distribution by age
- `GET /api/clinical/doctors` - Get list of doctors
- `GET /api/clinical/units` - Get list of units

### Health Check
- `GET /health` - Simple health check
- `GET /api/clinical/health` - Detailed health check

### Documentation
- `GET /docs` - Swagger UI documentation
- `GET /redoc` - ReDoc documentation

## Query Parameters

### Date Filtering
- `startDate` - Filter by start date (ISO string format)
- `endDate` - Filter by end date (ISO string format)

### Unit Filtering
- `unitId` - Filter by specific unit ID

### Other Filters
- `doctorId` - Filter by doctor ID
- `diagnosis` - Filter by diagnosis (partial match)
- `ageGroup` - Filter by age group (<16, 17-25, 26-40, 41-55, 56-65, >65)
- `gender` - Filter by gender (Male, Female, Other)

### Pagination
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 100, max: 1000)

## Database Integration

The application uses `vpc.addeddatetime` column for date filtering and `unitid` column for unit filtering, as requested.

## Development

The server runs on port 8000 by default and includes:
- Auto-reload on code changes
- Comprehensive error handling
- Structured logging
- CORS configuration for frontend integration
