import { Request, Response } from 'express';
import { ClinicalService } from '../services/clinicalService.js';
import { AnalyticsService } from '../services/analyticsService.js';
import { ClinicalModel } from '../models/clinical.js';
import { ClinicalDataQuery, StatisticsQuery } from '../types/clinical.js';
import { asyncHandler, AppError } from '../utils/errorHandler.js';
import { logger } from '../utils/logger.js';

export class ClinicalController {
  
  static getClinicalData = asyncHandler(async (req: Request, res: Response) => {
    const query: ClinicalDataQuery = {
      page: req.query.page ? parseInt(req.query.page as string) : 1,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 100,
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      doctorId: req.query.doctorId as string,
      diagnosis: req.query.diagnosis as string,
      ageGroup: req.query.ageGroup as string,
      gender: req.query.gender as string,
      unitId: req.query.unitId as string
    };

    // Validate pagination parameters
    if (query.page && query.page < 1) {
      throw new AppError('Page number must be greater than 0', 400);
    }
    
    if (query.limit && (query.limit < 1 || query.limit > 1000)) {
      throw new AppError('Limit must be between 1 and 1000', 400);
    }

    // Validate date format if provided
    if (query.startDate && isNaN(Date.parse(query.startDate))) {
      throw new AppError('Invalid start date format', 400);
    }
    
    if (query.endDate && isNaN(Date.parse(query.endDate))) {
      throw new AppError('Invalid end date format', 400);
    }

    logger.info('Fetching clinical data', { query });
    
    const result = await ClinicalService.getClinicalData(query);
    
    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getStatistics = asyncHandler(async (req: Request, res: Response) => {
    const query: StatisticsQuery = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      doctorId: req.query.doctorId as string,
      unitId: req.query.unitId as string
    };

    // Validate date format if provided
    if (query.startDate && isNaN(Date.parse(query.startDate))) {
      throw new AppError('Invalid start date format', 400);
    }
    
    if (query.endDate && isNaN(Date.parse(query.endDate))) {
      throw new AppError('Invalid end date format', 400);
    }

    logger.info('Fetching statistics', { query });
    
    const result = await ClinicalService.getStatistics(query);
    
    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getDiagnosisTrends = asyncHandler(async (req: Request, res: Response) => {
    const query: StatisticsQuery = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      doctorId: req.query.doctorId as string,
      unitId: req.query.unitId as string
    };

    logger.info('Fetching diagnosis trends', { query });
    
    const result = await ClinicalService.getDiagnosisTrends(query);
    
    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getDoctorActivity = asyncHandler(async (req: Request, res: Response) => {
    const query: StatisticsQuery = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      doctorId: req.query.doctorId as string,
      unitId: req.query.unitId as string
    };

    logger.info('Fetching doctor activity', { query });
    
    const result = await ClinicalService.getDoctorActivity(query);
    
    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getDiagnosisByAge = asyncHandler(async (req: Request, res: Response) => {
    const query: StatisticsQuery = {
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      doctorId: req.query.doctorId as string,
      unitId: req.query.unitId as string
    };

    logger.info('Fetching diagnosis by age', { query });
    
    const result = await ClinicalService.getDiagnosisByAge(query);
    
    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getDoctors = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Fetching doctors list');

    const { doctors } = await ClinicalService.getClinicalData({ limit: 1 }); // Just get doctors

    res.status(200).json({
      success: true,
      data: doctors,
      timestamp: new Date().toISOString()
    });
  });

  static getUnits = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Fetching units list');

    const units = await ClinicalModel.getUnits();

    res.status(200).json({
      success: true,
      data: units,
      timestamp: new Date().toISOString()
    });
  });

  static getHealthCheck = asyncHandler(async (req: Request, res: Response) => {
    // Test database connection
    const { db } = await import('../utils/database.js');
    const isDbHealthy = await db.testConnection();

    const healthStatus = {
      status: isDbHealthy ? 'healthy' : 'unhealthy',
      database: isDbHealthy ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    };

    const statusCode = isDbHealthy ? 200 : 503;

    res.status(statusCode).json({
      success: isDbHealthy,
      data: healthStatus
    });
  });

  static getSymptomClusters = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Fetching symptom clusters');

    const result = await AnalyticsService.getSymptomClusters();

    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getPatientDemographics = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Fetching patient demographics');

    const result = await AnalyticsService.getPatientDemographics();

    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });

  static getQualityMetrics = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Fetching quality metrics');

    const result = await AnalyticsService.getQualityMetrics();

    res.status(200).json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  });
}
