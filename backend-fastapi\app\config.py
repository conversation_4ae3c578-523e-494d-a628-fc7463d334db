import os
from typing import List
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # Database settings
    db_host: str = os.getenv("DB_HOST", "************")
    db_port: int = int(os.getenv("DB_PORT", "5432"))
    db_name: str = os.getenv("DB_NAME", "analytics")
    db_user: str = os.getenv("DB_USER", "analyser")
    db_password: str = os.getenv("DB_PASSWORD", "An@lyser123")
    
    # API settings
    api_port: int = int(os.getenv("API_PORT", "8000"))
    cors_origins: str = os.getenv("CORS_ORIGINS", "http://localhost:5173,http://localhost:5174")

    @property
    def cors_origins_list(self) -> List[str]:
        return self.cors_origins.split(",")
    
    # App settings
    app_name: str = "AI Clinical Dashboard API"
    version: str = "1.0.0"
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    @property
    def database_url(self) -> str:
        return f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    class Config:
        env_file = ".env"

settings = Settings()
