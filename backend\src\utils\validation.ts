import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler.js';

// Validation schemas
export interface ValidationSchema {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'date';
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: string[];
  };
}

// Query parameter validation
export const validateQuery = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const errors: string[] = [];

    for (const [field, rules] of Object.entries(schema)) {
      const value = req.query[field];

      // Check required fields
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field} is required`);
        continue;
      }

      // Skip validation if field is not provided and not required
      if (value === undefined || value === null || value === '') {
        continue;
      }

      // Type validation
      switch (rules.type) {
        case 'number':
          const numValue = Number(value);
          if (isNaN(numValue)) {
            errors.push(`${field} must be a valid number`);
          } else {
            if (rules.min !== undefined && numValue < rules.min) {
              errors.push(`${field} must be at least ${rules.min}`);
            }
            if (rules.max !== undefined && numValue > rules.max) {
              errors.push(`${field} must be at most ${rules.max}`);
            }
          }
          break;

        case 'date':
          const dateValue = new Date(value as string);
          if (isNaN(dateValue.getTime())) {
            errors.push(`${field} must be a valid date`);
          }
          break;

        case 'string':
          const strValue = value as string;
          if (rules.min !== undefined && strValue.length < rules.min) {
            errors.push(`${field} must be at least ${rules.min} characters`);
          }
          if (rules.max !== undefined && strValue.length > rules.max) {
            errors.push(`${field} must be at most ${rules.max} characters`);
          }
          if (rules.pattern && !rules.pattern.test(strValue)) {
            errors.push(`${field} has invalid format`);
          }
          if (rules.enum && !rules.enum.includes(strValue)) {
            errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
          }
          break;

        case 'boolean':
          const boolValue = value as string;
          if (!['true', 'false', '1', '0'].includes(boolValue.toLowerCase())) {
            errors.push(`${field} must be a boolean value`);
          }
          break;
      }
    }

    if (errors.length > 0) {
      throw new AppError(`Validation failed: ${errors.join(', ')}`, 400);
    }

    next();
  };
};

// Common validation schemas
export const clinicalDataQuerySchema: ValidationSchema = {
  page: { type: 'number', min: 1 },
  limit: { type: 'number', min: 1, max: 1000 },
  startDate: { type: 'date' },
  endDate: { type: 'date' },
  doctorId: { type: 'string', min: 1, max: 100 },
  diagnosis: { type: 'string', min: 1, max: 200 },
  ageGroup: { 
    type: 'string', 
    enum: ['<16', '17-25', '26-40', '41-55', '56-65', '>65'] 
  },
  gender: { 
    type: 'string', 
    enum: ['Male', 'Female', 'Other'] 
  }
};

export const statisticsQuerySchema: ValidationSchema = {
  startDate: { type: 'date' },
  endDate: { type: 'date' },
  doctorId: { type: 'string', min: 1, max: 100 }
};

// Sanitization functions
export const sanitizeString = (value: string): string => {
  return value.trim().replace(/[<>]/g, '');
};

export const sanitizeQuery = (req: Request, res: Response, next: NextFunction) => {
  for (const [key, value] of Object.entries(req.query)) {
    if (typeof value === 'string') {
      req.query[key] = sanitizeString(value);
    }
  }
  next();
};

// Rate limiting validation
export const validateRateLimit = (req: Request, res: Response, next: NextFunction) => {
  const limit = parseInt(req.query.limit as string) || 100;
  
  if (limit > 1000) {
    throw new AppError('Limit cannot exceed 1000 records per request', 400);
  }
  
  next();
};

// Date range validation
export const validateDateRange = (req: Request, res: Response, next: NextFunction) => {
  const startDate = req.query.startDate as string;
  const endDate = req.query.endDate as string;
  
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) {
      throw new AppError('Start date cannot be after end date', 400);
    }
    
    // Limit date range to 2 years
    const maxRange = 2 * 365 * 24 * 60 * 60 * 1000; // 2 years in milliseconds
    if (end.getTime() - start.getTime() > maxRange) {
      throw new AppError('Date range cannot exceed 2 years', 400);
    }
  }
  
  next();
};
