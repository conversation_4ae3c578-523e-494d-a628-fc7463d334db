import { ClinicalModel } from '../models/clinical.js';
import { 
  ClinicalDataQuery, 
  StatisticsQuery,
  StatisticsResponse,
  DiagnosisTrendData,
  DoctorActivityData,
  DiagnosisByAgeData,
  Visit,
  Doctor
} from '../types/clinical.js';
import { logger } from '../utils/logger.js';

export class ClinicalService {
  
  static async getClinicalData(query: ClinicalDataQuery) {
    try {
      const { data, total } = await ClinicalModel.getClinicalData(query);
      const doctors = await ClinicalModel.getDoctors();
      
      return {
        visits: data,
        doctors,
        totalCount: total,
        page: query.page || 1,
        limit: query.limit || 100
      };
    } catch (error) {
      logger.error('Error in getClinicalData service', { error });
      throw error;
    }
  }

  static async getStatistics(query: StatisticsQuery = {}): Promise<StatisticsResponse> {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({
        startDate: query.startDate,
        endDate: query.endDate,
        doctorId: query.doctorId,
        limit: 10000 // Get all data for statistics
      });

      if (visits.length === 0) {
        return this.getEmptyStatistics();
      }

      // Calculate statistics
      const totalPatients = new Set(visits.map(v => v.patient.id)).size;
      const totalVisits = visits.length;
      const totalDoctors = new Set(visits.map(v => v.doctor.id)).size;
      const referralRate = (visits.filter(v => v.referred).length / visits.length) * 100;
      const polypharmacyCount = visits.filter(v => v.medications.length >= 5).length;

      // Top complaint
      const complaintCounts = visits.reduce((acc, visit) => {
        acc[visit.chiefComplaint] = (acc[visit.chiefComplaint] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      const topComplaint = Object.entries(complaintCounts).sort((a, b) => b[1] - a[1])[0];

      // Top diagnosis
      const diagnosisCounts = visits.reduce((acc, visit) => {
        acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      const topDiagnosis = Object.entries(diagnosisCounts).sort((a, b) => b[1] - a[1])[0];

      // Top medication
      const medicationCounts = visits.flatMap(v => v.medications).reduce((acc, med) => {
        acc[med] = (acc[med] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      const topMedication = Object.entries(medicationCounts).sort((a, b) => b[1] - a[1])[0];

      // Average investigations
      const avgInvestigations = visits.reduce((sum, v) => sum + v.investigations.length, 0) / visits.length;

      return {
        totalPatients,
        totalVisits,
        totalDoctors,
        referralRate: Math.round(referralRate * 100) / 100,
        polypharmacyCount,
        topComplaint: { name: topComplaint?.[0] || 'N/A', count: topComplaint?.[1] || 0 },
        topDiagnosis: { name: topDiagnosis?.[0] || 'N/A', count: topDiagnosis?.[1] || 0 },
        topMedication: { name: topMedication?.[0] || 'N/A', count: topMedication?.[1] || 0 },
        avgInvestigations: avgInvestigations.toFixed(1),
        mostFrequentPair: `${topComplaint?.[0] || 'N/A'} → ${topDiagnosis?.[0] || 'N/A'}`
      };
    } catch (error) {
      logger.error('Error in getStatistics service', { error });
      throw error;
    }
  }

  static async getDiagnosisTrends(query: StatisticsQuery = {}): Promise<DiagnosisTrendData> {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({
        startDate: query.startDate,
        endDate: query.endDate,
        doctorId: query.doctorId,
        limit: 10000
      });

      const monthlyData: Record<string, Record<string, number>> = {};
      const top5Diagnoses = Object.entries(visits.reduce((acc, visit) => {
        acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)).sort((a, b) => b[1] - a[1]).slice(0, 5).map(d => d[0]);

      visits.forEach(visit => {
        if (top5Diagnoses.includes(visit.diagnosis)) {
          const month = new Date(visit.date).toLocaleString('default', { month: 'short' });
          if (!monthlyData[month]) {
            monthlyData[month] = {};
          }
          monthlyData[month][visit.diagnosis] = (monthlyData[month][visit.diagnosis] || 0) + 1;
        }
      });

      const sortedMonths = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const labels = sortedMonths.filter(m => monthlyData[m]);
      
      const datasets = top5Diagnoses.map(diagnosis => ({
        label: diagnosis,
        data: labels.map(month => monthlyData[month]?.[diagnosis] || 0),
      }));

      return { labels, datasets };
    } catch (error) {
      logger.error('Error in getDiagnosisTrends service', { error });
      throw error;
    }
  }

  static async getDoctorActivity(query: StatisticsQuery = {}): Promise<DoctorActivityData[]> {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({
        startDate: query.startDate,
        endDate: query.endDate,
        limit: 10000
      });
      
      const doctors = await ClinicalModel.getDoctors();

      return doctors.map(doctor => {
        const docVisits = visits.filter(v => v.doctor.id === doctor.id);
        if (docVisits.length === 0) {
          return { name: doctor.name, avgPatients: '0', topDiagnosis: 'N/A', avgMeds: '0' };
        }
        
        const diagnosisCounts = docVisits.reduce((acc, v) => {
          acc[v.diagnosis] = (acc[v.diagnosis] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        const topDiagnosis = Object.keys(diagnosisCounts).length > 0
          ? Object.entries(diagnosisCounts).sort((a, b) => b[1] - a[1])[0][0]
          : 'N/A';
        
        const avgMeds = docVisits.reduce((sum, v) => sum + v.medications.length, 0) / docVisits.length;
        
        // Assuming data is for a 365 day period for avg patients/day
        const avgPatients = (docVisits.length / 365).toFixed(1);

        return {
          name: doctor.name,
          avgPatients,
          topDiagnosis,
          avgMeds: avgMeds.toFixed(1),
        };
      });
    } catch (error) {
      logger.error('Error in getDoctorActivity service', { error });
      throw error;
    }
  }

  static async getDiagnosisByAge(query: StatisticsQuery = {}): Promise<DiagnosisByAgeData> {
    try {
      const { data: visits } = await ClinicalModel.getClinicalData({
        startDate: query.startDate,
        endDate: query.endDate,
        doctorId: query.doctorId,
        limit: 10000
      });

      const ageGroups = { '0-18': [], '19-40': [], '41-65': [], '65+': [] };
      const diagnoses = [...new Set(visits.map(v => v.diagnosis))];
      const data: Record<string, Record<string, number>> = {};

      diagnoses.forEach(d => {
        data[d] = { '0-18': 0, '19-40': 0, '41-65': 0, '65+': 0 };
      });

      visits.forEach(v => {
        let group: keyof typeof ageGroups;
        if (v.patient.age <= 18) group = '0-18';
        else if (v.patient.age <= 40) group = '19-40';
        else if (v.patient.age <= 65) group = '41-65';
        else group = '65+';
        if(data[v.diagnosis]) {
          data[v.diagnosis][group]++;
        }
      });
      
      const top5Diagnoses = Object.entries(visits.reduce((acc, visit) => {
        acc[visit.diagnosis] = (acc[visit.diagnosis] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)).sort((a, b) => b[1] - a[1]).slice(0, 5).map(d => d[0]);

      const labels = Object.keys(ageGroups);
      const datasets = top5Diagnoses.map(diagnosis => ({
        label: diagnosis,
        data: labels.map(ageGroup => data[diagnosis][ageGroup as keyof typeof ageGroups])
      }));

      return { labels, datasets };
    } catch (error) {
      logger.error('Error in getDiagnosisByAge service', { error });
      throw error;
    }
  }

  private static getEmptyStatistics(): StatisticsResponse {
    return {
      totalPatients: 0,
      totalVisits: 0,
      totalDoctors: 0,
      referralRate: 0,
      polypharmacyCount: 0,
      topComplaint: { name: 'N/A', count: 0 },
      topDiagnosis: { name: 'N/A', count: 0 },
      topMedication: { name: 'N/A', count: 0 },
      avgInvestigations: '0',
      mostFrequentPair: 'N/A → N/A'
    };
  }
}
