{"patients": [{"id": "pat001", "personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1985-03-15", "age": 39, "gender": "Female", "ssn": "***-**-1234", "phone": "******-0101", "email": "<EMAIL>", "address": {"street": "123 Oak Street", "city": "Springfield", "state": "IL", "zipCode": "62701"}}, "insurance": {"provider": "HealthFirst Insurance", "policyNumber": "HF123456789", "groupNumber": "GRP001", "copay": 25}, "emergencyContact": {"name": "<PERSON>", "relationship": "Spouse", "phone": "******-0102"}, "medicalHistory": {"allergies": ["Penicillin", "Shellfish"], "chronicConditions": ["Hypertension", "Diabetes Type 2"], "surgeries": [{"procedure": "Appendectomy", "date": "2010-07-15", "hospital": "City General Hospital"}], "familyHistory": ["Diabetes", "Heart Disease", "Cancer"]}, "currentMedications": [{"name": "Lisinopril", "dosage": "10mg", "frequency": "Once daily", "prescribedDate": "2023-01-15"}, {"name": "Metformin", "dosage": "500mg", "frequency": "Twice daily", "prescribedDate": "2023-01-15"}], "vitalSigns": {"lastRecorded": "2024-01-15", "bloodPressure": "135/85", "heartRate": 78, "temperature": 98.6, "respiratoryRate": 16, "oxygenSaturation": 98, "weight": 165, "height": "5'6\"", "bmi": 26.6}}, {"id": "pat002", "personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1972-11-08", "age": 52, "gender": "Male", "ssn": "***-**-5678", "phone": "******-0201", "email": "<EMAIL>", "address": {"street": "456 Pine Avenue", "city": "Springfield", "state": "IL", "zipCode": "62702"}}, "insurance": {"provider": "MediCare Plus", "policyNumber": "MP987654321", "groupNumber": "GRP002", "copay": 15}, "emergencyContact": {"name": "<PERSON>", "relationship": "Spouse", "phone": "******-0202"}, "medicalHistory": {"allergies": ["Latex"], "chronicConditions": ["Asthma", "High Cholesterol"], "surgeries": [], "familyHistory": ["Asthma", "Stroke"]}, "currentMedications": [{"name": "Albuterol Inhaler", "dosage": "90mcg", "frequency": "As needed", "prescribedDate": "2023-06-10"}, {"name": "Atorvas<PERSON><PERSON>", "dosage": "20mg", "frequency": "Once daily", "prescribedDate": "2023-03-20"}], "vitalSigns": {"lastRecorded": "2024-01-10", "bloodPressure": "125/80", "heartRate": 72, "temperature": 98.4, "respiratoryRate": 18, "oxygenSaturation": 96, "weight": 180, "height": "5'10\"", "bmi": 25.8}}, {"id": "pat003", "personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1995-07-22", "age": 29, "gender": "Female", "ssn": "***-**-9012", "phone": "******-0301", "email": "maria.rod<PERSON><PERSON><PERSON>@email.com", "address": {"street": "789 Elm Drive", "city": "Springfield", "state": "IL", "zipCode": "62703"}}, "insurance": {"provider": "Basic Health Plan", "policyNumber": "BHP456789123", "groupNumber": "GRP003", "copay": 40}, "emergencyContact": {"name": "<PERSON>", "relationship": "Father", "phone": "******-0302"}, "medicalHistory": {"allergies": ["None known"], "chronicConditions": [], "surgeries": [], "familyHistory": ["Diabetes", "Hypertension"]}, "currentMedications": [], "vitalSigns": {"lastRecorded": "2024-01-05", "bloodPressure": "110/70", "heartRate": 68, "temperature": 98.2, "respiratoryRate": 14, "oxygenSaturation": 99, "weight": 135, "height": "5'4\"", "bmi": 23.2}}], "recentVisits": [{"visitId": "v001", "patientId": "pat001", "date": "2024-01-15", "type": "Follow-up V<PERSON>t", "doctor": "Dr. <PERSON>", "department": "Internal Medicine", "chiefComplaint": "Blood pressure check", "assessment": "Hypertension well controlled, diabetes stable", "plan": "Continue current medications, follow up in 3 months", "vitals": {"bloodPressure": "135/85", "heartRate": 78, "temperature": 98.6, "weight": 165}, "labsOrdered": ["HbA1c", "Lipid Panel"], "nextAppointment": "2024-04-15"}, {"visitId": "v002", "patientId": "pat002", "date": "2024-01-10", "type": "<PERSON><PERSON><PERSON>", "doctor": "Dr. <PERSON>", "department": "Internal Medicine", "chiefComplaint": "Annual physical", "assessment": "Overall good health, asthma well controlled", "plan": "Continue current medications, annual mammogram recommended", "vitals": {"bloodPressure": "125/80", "heartRate": 72, "temperature": 98.4, "weight": 180}, "labsOrdered": ["CBC", "Lipid Panel", "PSA"], "nextAppointment": "2025-01-10"}]}