# AI Clinical Dashboard Backend API

A robust Node.js/Express API for clinical data analytics and management.

## Features

- **Clinical Data Management**: Fetch and filter clinical visits, patient data, and doctor information
- **Analytics & Statistics**: Generate comprehensive clinical statistics and trends
- **Data Visualization Support**: Endpoints optimized for charts and dashboards
- **AI Integration**: Symptom clustering and analysis capabilities
- **Security**: Rate limiting, input validation, and sanitization
- **Database Integration**: PostgreSQL with connection pooling
- **Error Handling**: Comprehensive error handling and logging
- **TypeScript**: Full TypeScript support for type safety

## Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. Start development server:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
npm start
```

## Environment Variables

```env
# Database Configuration
DB_HOST=************
DB_PORT=5432
DB_NAME=analytics
DB_USER=analyser
DB_PASSWORD=An@lyser123

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## API Endpoints

### Base URL
```
http://localhost:3001/api/clinical
```

### Health Check
```http
GET /health
```

### Clinical Data
```http
GET /data?page=1&limit=100&startDate=2023-01-01&endDate=2024-12-31
```

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 100, max: 1000)
- `startDate` (string): Filter by start date (ISO format)
- `endDate` (string): Filter by end date (ISO format)
- `doctorId` (string): Filter by doctor ID
- `diagnosis` (string): Filter by diagnosis (partial match)
- `ageGroup` (string): Filter by age group (`<16`, `17-25`, `26-40`, `41-55`, `56-65`, `>65`)
- `gender` (string): Filter by gender (`Male`, `Female`, `Other`)

### Statistics
```http
GET /statistics?startDate=2023-01-01&endDate=2024-12-31
```

### Diagnosis Trends
```http
GET /diagnosis-trends?startDate=2023-01-01&endDate=2024-12-31
```

### Doctor Activity
```http
GET /doctor-activity?startDate=2023-01-01&endDate=2024-12-31
```

### Diagnosis by Age
```http
GET /diagnosis-by-age?startDate=2023-01-01&endDate=2024-12-31
```

### Doctors List
```http
GET /doctors
```

### Symptom Clusters
```http
GET /symptom-clusters
```

### Patient Demographics
```http
GET /patient-demographics
```

### Quality Metrics
```http
GET /quality-metrics
```

## Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:

```json
{
  "error": {
    "message": "Error description",
    "statusCode": 400,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "path": "/api/clinical/data",
    "method": "GET"
  }
}
```

## Database Schema

The API expects the following database views and tables:

- `vw_patcomplaints`: Patient complaints view
- `vw_patdiagnosis`: Patient diagnosis view  
- `dim_encounter`: Encounter dimension table
- `dim_patient`: Patient dimension table
- `dim_doctor`: Doctor dimension table

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: All query parameters are validated and sanitized
- **CORS**: Configured for specific frontend origins
- **Helmet**: Security headers middleware
- **Error Handling**: Secure error responses without sensitive data leaks

## Development

### Scripts

- `npm run dev`: Start development server with hot reload
- `npm run build`: Build TypeScript to JavaScript
- `npm start`: Start production server
- `npm test`: Run tests

### Project Structure

```
backend/
├── src/
│   ├── controllers/     # Request handlers
│   ├── models/         # Data models
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── types/          # TypeScript types
│   ├── utils/          # Utilities
│   └── server.ts       # Main server file
├── dist/               # Compiled JavaScript
├── .env                # Environment variables
└── package.json
```

## Monitoring

The API includes comprehensive logging for:
- Database queries and performance
- API requests and responses
- Error tracking
- Health checks

## Contributing

1. Follow TypeScript best practices
2. Add proper error handling
3. Include input validation
4. Write tests for new features
5. Update documentation
