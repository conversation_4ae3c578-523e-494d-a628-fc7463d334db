from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime
from app.database import db
from app.models import (
    ClinicalDataQuery, StatisticsQuery, Visit, Doctor, Unit, Patient,
    Statistics, TopItem, DiagnosisTrends, DoctorActivity, DiagnosisByAge,
    ClinicalDataResponse, DiagnosisTrendDataset
)

logger = logging.getLogger(__name__)

class ClinicalService:
    
    # Base query from the provided SQL
    BASE_QUERY = """
        with cteopdclinicA as (
          select vpc.*,vpd.diagnoses from vw_patcomplaints vpc
          inner join vw_patdiagnosis vpd on 
          vpc.visitid = vpd.visitid 
          and vpc.opdipd = vpd.opdipd
          and vpc.patientid = vpd.patientid 
          and vpc.unitid = vpd.unitid
          where vpc.opdipd = 0
        ) 
        select A.*,dp.gender,
        CASE
          WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) < 16::double precision THEN '<16'::text
          WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 17::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 25::double precision THEN '17-25'::text
          WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 26::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 40::double precision THEN '26-40'::text
          WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 41::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 55::double precision THEN '41-55'::text
          WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 56::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 65::double precision THEN '56-65'::text
          ELSE '>65'::text
        END AS age_group,
        dd.doctor_id,
        'Dr. ' || dd.firstname || ' ' || dd.lastname as Doctorname,
        null as medications,
        null as investigations
        from cteopdclinicA A
        inner join dim_encounter de on A.visitid = de.encounterid
        inner join dim_patient dp on dp.patient_id = A.patientid 
        inner join dim_doctor dd on dd.doctor_id = de.doctormodalityid
    """
    
    @staticmethod
    def get_age_from_age_group(age_group: str) -> int:
        """Convert age group to actual age (middle of range)"""
        age_ranges = {
            '<16': (0, 15),
            '17-25': (17, 25),
            '26-40': (26, 40),
            '41-55': (41, 55),
            '56-65': (56, 65),
            '>65': (66, 150)
        }
        
        if age_group in age_ranges:
            min_age, max_age = age_ranges[age_group]
            return (min_age + max_age) // 2
        return 30  # Default age
    
    @staticmethod
    def transform_row_to_visit(row: Dict[str, Any]) -> Visit:
        """Transform database row to Visit model"""
        # Generate a mock date since the database doesn't have a date column
        mock_date = datetime(2024, 10, 6, 18, 30, 0)
        
        # Normalize gender value
        gender_value = row['gender']
        if gender_value == 'FEMALE':
            gender_value = 'Female'
        elif gender_value == 'MALE':
            gender_value = 'Male'
        elif gender_value not in ['Male', 'Female', 'Other']:
            gender_value = 'Other'

        return Visit(
            id=str(row['visitid']),
            patient=Patient(
                id=str(row['patientid']),
                age=ClinicalService.get_age_from_age_group(row['age_group']),
                gender=gender_value
            ),
            doctor=Doctor(
                id=str(row['doctor_id']),
                name=row['doctorname']
            ),
            date=mock_date.isoformat(),
            chiefComplaint=row.get('chiefcomplaint', ''),
            diagnosis=row.get('diagnoses', ''),
            medications=row.get('medications', '').split(',') if row.get('medications') else [],
            investigations=row.get('investigations', '').split(',') if row.get('investigations') else [],
            referred=False  # Mock referral data
        )
    
    @staticmethod
    def get_clinical_data(query: ClinicalDataQuery) -> ClinicalDataResponse:
        """Get clinical data with filtering and pagination"""
        try:
            where_conditions = []
            query_params = []
            param_index = 1
            
            # Add date filters using vpc.addeddatetime column
            if query.start_date:
                where_conditions.append("A.addeddatetime >= %s")
                query_params.append(query.start_date)

            if query.end_date:
                where_conditions.append("A.addeddatetime <= %s")
                query_params.append(query.end_date)

            # Add unit filter
            if query.unit_id:
                where_conditions.append("A.unitid = %s")
                query_params.append(query.unit_id)

            # Add doctor filter
            if query.doctor_id:
                where_conditions.append("dd.doctor_id = %s")
                query_params.append(query.doctor_id)

            # Add diagnosis filter
            if query.diagnosis:
                where_conditions.append("A.diagnoses ILIKE %s")
                query_params.append(f"%{query.diagnosis}%")
            
            # Add age group filter
            if query.age_group:
                age_group_condition = """
                    CASE
                      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) < 16::double precision THEN '<16'::text
                      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 17::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 25::double precision THEN '17-25'::text
                      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 26::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 40::double precision THEN '26-40'::text
                      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 41::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 55::double precision THEN '41-55'::text
                      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 56::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 65::double precision THEN '56-65'::text
                      ELSE '>65'::text
                    END = %s
                """
                where_conditions.append(age_group_condition)
                query_params.append(query.age_group.value)

            # Add gender filter
            if query.gender:
                where_conditions.append("dp.gender = %s")
                query_params.append(query.gender.value)
            
            # Build the complete query
            full_query = ClinicalService.BASE_QUERY
            if where_conditions:
                full_query += f" WHERE {' AND '.join(where_conditions)}"
            
            # Add ordering
            full_query += " ORDER BY A.visitid DESC"
            
            # Count query for total
            count_query = f"SELECT COUNT(*) as total FROM ({full_query}) as counted_data"
            count_result = db.execute_single_query(count_query, query_params)
            total = count_result['total'] if count_result else 0
            
            # Add pagination to main query
            offset = (query.page - 1) * query.limit
            full_query += " LIMIT %s OFFSET %s"
            query_params.extend([query.limit, offset])
            
            # Execute main query
            results = db.execute_query(full_query, query_params)
            
            # Transform results to Visit objects
            visits = [ClinicalService.transform_row_to_visit(row) for row in results]
            
            # Get doctors
            doctors = ClinicalService.get_doctors()
            
            return ClinicalDataResponse(
                visits=visits,
                doctors=doctors,
                totalCount=total,
                page=query.page,
                limit=query.limit
            )
            
        except Exception as e:
            logger.error(f"Error fetching clinical data: {e}")
            raise
    
    @staticmethod
    def get_doctors() -> List[Doctor]:
        """Get list of all doctors"""
        try:
            query = """
                SELECT DISTINCT dd.doctor_id as id, 'Dr. ' || dd.firstname || ' ' || dd.lastname as name
                FROM dim_doctor dd
                INNER JOIN dim_encounter de ON dd.doctor_id = de.doctormodalityid
                ORDER BY name
            """
            
            results = db.execute_query(query)
            return [Doctor(id=str(row['id']), name=row['name']) for row in results]
            
        except Exception as e:
            logger.error(f"Error fetching doctors: {e}")
            raise
    
    @staticmethod
    def get_units() -> List[Unit]:
        """Get list of all units"""
        try:
            query = """
                SELECT DISTINCT 
                  unitid as id, 
                  CASE 
                    WHEN unitid = '1' THEN 'Emergency Department'
                    WHEN unitid = '2' THEN 'Internal Medicine'
                    WHEN unitid = '3' THEN 'Cardiology'
                    WHEN unitid = '4' THEN 'Pediatrics'
                    WHEN unitid = '5' THEN 'Surgery'
                    WHEN unitid = '6' THEN 'Orthopedics'
                    WHEN unitid = '7' THEN 'Neurology'
                    WHEN unitid = '8' THEN 'Oncology'
                    ELSE 'Unit ' || unitid
                  END as name
                FROM vw_patcomplaints
                WHERE unitid IS NOT NULL 
                  AND unitid != ''
                  AND LENGTH(TRIM(unitid)) > 0
                ORDER BY unitid
            """
            
            results = db.execute_query(query)
            return [Unit(id=str(row['id']), name=row['name']) for row in results]
            
        except Exception as e:
            logger.error(f"Error fetching units from database, returning mock data: {e}")
            # Return mock data as fallback
            return [
                Unit(id='1', name='Emergency Department'),
                Unit(id='2', name='Internal Medicine'),
                Unit(id='3', name='Cardiology'),
                Unit(id='4', name='Pediatrics'),
                Unit(id='5', name='Surgery'),
                Unit(id='6', name='Orthopedics'),
                Unit(id='7', name='Neurology'),
                Unit(id='8', name='Oncology')
            ]

    @staticmethod
    def get_statistics(query: StatisticsQuery) -> Statistics:
        """Get clinical statistics"""
        try:
            # For now, return mock statistics
            # In a real implementation, you would calculate these from the database
            return Statistics(
                totalPatients=1250,
                totalVisits=3420,
                totalDoctors=45,
                referralRate=12.5,
                polypharmacyCount=156,
                topComplaint=TopItem(name="Chest Pain", count=245),
                topDiagnosis=TopItem(name="Hypertension", count=189),
                topMedication=TopItem(name="Aspirin", count=312),
                avgInvestigations="2.3",
                mostFrequentPair="Chest Pain → Hypertension"
            )
        except Exception as e:
            logger.error(f"Error fetching statistics: {e}")
            raise

    @staticmethod
    def get_diagnosis_trends(query: StatisticsQuery) -> DiagnosisTrends:
        """Get diagnosis trends over time"""
        try:
            # Mock data for diagnosis trends
            return DiagnosisTrends(
                labels=["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                datasets=[
                    DiagnosisTrendDataset(label="Hypertension", data=[45, 52, 48, 61, 55, 67]),
                    DiagnosisTrendDataset(label="Diabetes", data=[32, 38, 35, 42, 39, 45]),
                    DiagnosisTrendDataset(label="Chest Pain", data=[28, 31, 29, 35, 33, 38]),
                    DiagnosisTrendDataset(label="Headache", data=[22, 25, 23, 28, 26, 31]),
                    DiagnosisTrendDataset(label="Back Pain", data=[18, 21, 19, 24, 22, 27])
                ]
            )
        except Exception as e:
            logger.error(f"Error fetching diagnosis trends: {e}")
            raise

    @staticmethod
    def get_doctor_activity(query: StatisticsQuery) -> List[DoctorActivity]:
        """Get doctor activity metrics"""
        try:
            # Mock data for doctor activity
            return [
                DoctorActivity(name="Dr. Smith", avgPatients="23.5", topDiagnosis="Hypertension", avgMeds="2.8"),
                DoctorActivity(name="Dr. Johnson", avgPatients="21.2", topDiagnosis="Diabetes", avgMeds="3.1"),
                DoctorActivity(name="Dr. Williams", avgPatients="19.8", topDiagnosis="Chest Pain", avgMeds="2.5"),
                DoctorActivity(name="Dr. Brown", avgPatients="18.6", topDiagnosis="Headache", avgMeds="2.2"),
                DoctorActivity(name="Dr. Davis", avgPatients="17.3", topDiagnosis="Back Pain", avgMeds="2.0")
            ]
        except Exception as e:
            logger.error(f"Error fetching doctor activity: {e}")
            raise

    @staticmethod
    def get_diagnosis_by_age(query: StatisticsQuery) -> DiagnosisByAge:
        """Get diagnosis distribution by age groups"""
        try:
            # Mock data for diagnosis by age
            return DiagnosisByAge(
                labels=["<16", "17-25", "26-40", "41-55", "56-65", ">65"],
                datasets=[
                    DiagnosisTrendDataset(label="Hypertension", data=[2, 8, 25, 45, 67, 89]),
                    DiagnosisTrendDataset(label="Diabetes", data=[1, 5, 18, 32, 48, 62]),
                    DiagnosisTrendDataset(label="Chest Pain", data=[3, 12, 22, 28, 35, 41]),
                    DiagnosisTrendDataset(label="Headache", data=[8, 15, 20, 18, 12, 8]),
                    DiagnosisTrendDataset(label="Back Pain", data=[2, 6, 15, 25, 22, 18])
                ]
            )
        except Exception as e:
            logger.error(f"Error fetching diagnosis by age: {e}")
            raise
