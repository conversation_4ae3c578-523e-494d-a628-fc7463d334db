
import React from 'react';

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'indigo' | 'purple' | 'amber';
}

const colorClasses = {
  blue: { bg: 'bg-blue-100', text: 'text-blue-600' },
  green: { bg: 'bg-green-100', text: 'text-green-600' },
  indigo: { bg: 'bg-indigo-100', text: 'text-indigo-600' },
  purple: { bg: 'bg-purple-100', text: 'text-purple-600' },
  amber: { bg: 'bg-amber-100', text: 'text-amber-600' },
};

const StatCard: React.FC<StatCardProps> = ({ title, value, change, icon, color }) => {
  const classes = colorClasses[color];
  return (
    <div className="bg-white p-5 rounded-lg shadow flex items-start justify-between transition-all hover:shadow-lg hover:-translate-y-1">
      <div>
        <p className="text-sm font-medium text-gray-500">{title}</p>
        <p className="text-2xl font-bold text-neutral-dark mt-1 truncate">{value}</p>
        <p className="text-xs text-gray-400 mt-1">{change}</p>
      </div>
      <div className={`p-3 rounded-full ${classes.bg} ${classes.text}`}>
        {icon}
      </div>
    </div>
  );
};

export default StatCard;
