import { useState, useEffect, useCallback } from 'react';
import { clinicalApi, StatisticsResponse, DiagnosisTrendData, DoctorActivityData, DiagnosisByAgeData } from '../services/clinicalApi';
import { Visit, Doctor, SymptomCluster } from '../types';

interface ClinicalDataState {
  visits: Visit[];
  doctors: Doctor[];
  statistics: StatisticsResponse | null;
  diagnosisTrends: DiagnosisTrendData | null;
  doctorActivity: DoctorActivityData[];
  diagnosisByAge: DiagnosisByAgeData | null;
  symptomClusters: SymptomCluster[];
  loading: boolean;
  error: string | null;
}

interface UseApiClinicalDataOptions {
  autoFetch?: boolean;
  limit?: number;
  startDate?: string;
  endDate?: string;
  doctorId?: string;
}

export const useApiClinicalData = (options: UseApiClinicalDataOptions = {}) => {
  const { autoFetch = true, limit = 1000, startDate, endDate, doctorId } = options;

  const [state, setState] = useState<ClinicalDataState>({
    visits: [],
    doctors: [],
    statistics: null,
    diagnosisTrends: null,
    doctorActivity: [],
    diagnosisByAge: null,
    symptomClusters: [],
    loading: false,
    error: null,
  });

  const fetchAllData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const query = { startDate, endDate, doctorId };

      // Fetch all data in parallel
      const [
        clinicalData,
        statistics,
        diagnosisTrends,
        doctorActivity,
        diagnosisByAge,
        symptomClusters
      ] = await Promise.all([
        clinicalApi.getClinicalData({ ...query, limit }),
        clinicalApi.getStatistics(query),
        clinicalApi.getDiagnosisTrends(query),
        clinicalApi.getDoctorActivity(query),
        clinicalApi.getDiagnosisByAge(query),
        clinicalApi.getSymptomClusters()
      ]);

      setState(prev => ({
        ...prev,
        visits: clinicalData.visits,
        doctors: clinicalData.doctors,
        statistics,
        diagnosisTrends,
        doctorActivity,
        diagnosisByAge,
        symptomClusters,
        loading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Error fetching clinical data:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch data',
      }));
    }
  }, [limit, startDate, endDate, doctorId]);

  const fetchClinicalData = useCallback(async (customQuery = {}) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const query = { startDate, endDate, doctorId, limit, ...customQuery };
      const clinicalData = await clinicalApi.getClinicalData(query);

      setState(prev => ({
        ...prev,
        visits: clinicalData.visits,
        doctors: clinicalData.doctors,
        loading: false,
        error: null,
      }));

      return clinicalData;
    } catch (error) {
      console.error('Error fetching clinical data:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch clinical data',
      }));
      throw error;
    }
  }, [limit, startDate, endDate, doctorId]);

  const refreshData = useCallback(() => {
    return fetchAllData();
  }, [fetchAllData]);

  // Auto-fetch data on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchAllData();
    }
  }, [autoFetch, fetchAllData]);

  // Transform data to match the existing useClinicalData hook interface
  const clinicalSummary = state.statistics ? {
    topComplaint: state.statistics.topComplaint,
    topDiagnosis: state.statistics.topDiagnosis,
    topMedication: state.statistics.topMedication,
    avgInvestigations: state.statistics.avgInvestigations,
    mostFrequentPair: state.statistics.mostFrequentPair,
  } : null;

  const qualityMetrics = state.statistics ? {
    totalPatients: state.statistics.totalPatients,
    referralRate: state.statistics.referralRate,
    polypharmacyCount: state.statistics.polypharmacyCount,
  } : null;

  return {
    // Data
    visits: state.visits,
    doctors: state.doctors,
    clinicalSummary,
    diagnosisTrends: state.diagnosisTrends,
    doctorActivity: state.doctorActivity,
    diagnosisByAge: state.diagnosisByAge,
    qualityMetrics,
    symptomClusters: state.symptomClusters,
    
    // State
    loading: state.loading,
    error: state.error,
    
    // Actions
    fetchClinicalData,
    refreshData,
    
    // Raw statistics for advanced use cases
    statistics: state.statistics,
  };
};
