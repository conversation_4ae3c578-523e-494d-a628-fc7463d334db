import React, { useState, useEffect } from 'react';
import { CalendarIcon, FilterIcon } from './icons/Icons';
import { clinicalApi } from '../services/clinicalApi';

interface Unit {
  id: string;
  name: string;
}

interface ClinicalFiltersProps {
  onFiltersChange: (filters: {
    startDate?: string;
    endDate?: string;
    unitId?: string;
    doctorId?: string;
  }) => void;
  doctors: Array<{ id: string; name: string }>;
  loading?: boolean;
}

const ClinicalFilters: React.FC<ClinicalFiltersProps> = ({
  onFiltersChange,
  doctors,
  loading = false
}) => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [unitId, setUnitId] = useState('');
  const [doctorId, setDoctorId] = useState('');
  const [units, setUnits] = useState<Unit[]>([]);

  // Fetch units from API
  useEffect(() => {
    const fetchUnits = async () => {
      try {
        const fetchedUnits = await clinicalApi.getUnits();
        setUnits(fetchedUnits);
      } catch (error) {
        console.error('Error fetching units:', error);
        // Fallback to mock data if API fails
        const mockUnits: Unit[] = [
          { id: '1', name: 'Emergency Department' },
          { id: '2', name: 'Internal Medicine' },
          { id: '3', name: 'Cardiology' },
          { id: '4', name: 'Pediatrics' },
          { id: '5', name: 'Surgery' },
          { id: '6', name: 'Orthopedics' },
          { id: '7', name: 'Neurology' },
          { id: '8', name: 'Oncology' }
        ];
        setUnits(mockUnits);
      }
    };

    fetchUnits();
  }, []);

  // Handle filter changes
  useEffect(() => {
    const filters: any = {};
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (unitId) filters.unitId = unitId;
    if (doctorId) filters.doctorId = doctorId;
    
    onFiltersChange(filters);
  }, [startDate, endDate, unitId, doctorId, onFiltersChange]);

  const clearFilters = () => {
    setStartDate('');
    setEndDate('');
    setUnitId('');
    setDoctorId('');
  };

  const hasActiveFilters = startDate || endDate || unitId || doctorId;

  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <FilterIcon className="w-5 h-5 text-brand-primary" />
          <h3 className="text-lg font-semibold text-neutral-dark">Filters</h3>
        </div>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-brand-primary hover:text-brand-secondary transition-colors"
            disabled={loading}
          >
            Clear All
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* From Date */}
        <div>
          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
            From Date
          </label>
          <div className="relative">
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
              disabled={loading}
            />
            <CalendarIcon className="absolute right-3 top-2.5 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* To Date */}
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
            To Date
          </label>
          <div className="relative">
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              min={startDate}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
              disabled={loading}
            />
            <CalendarIcon className="absolute right-3 top-2.5 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>

        {/* Unit Selector */}
        <div>
          <label htmlFor="unitId" className="block text-sm font-medium text-gray-700 mb-1">
            Unit
          </label>
          <select
            id="unitId"
            value={unitId}
            onChange={(e) => setUnitId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
            disabled={loading}
          >
            <option value="">All Units</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.id}>
                {unit.name}
              </option>
            ))}
          </select>
        </div>

        {/* Doctor Selector */}
        <div>
          <label htmlFor="doctorId" className="block text-sm font-medium text-gray-700 mb-1">
            Doctor
          </label>
          <select
            id="doctorId"
            value={doctorId}
            onChange={(e) => setDoctorId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary"
            disabled={loading}
          >
            <option value="">All Doctors</option>
            {doctors.map((doctor) => (
              <option key={doctor.id} value={doctor.id}>
                {doctor.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {startDate && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand-dark">
                From: {new Date(startDate).toLocaleDateString()}
                <button
                  onClick={() => setStartDate('')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-brand-dark hover:bg-brand-secondary hover:text-white"
                  disabled={loading}
                >
                  ×
                </button>
              </span>
            )}
            {endDate && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand-dark">
                To: {new Date(endDate).toLocaleDateString()}
                <button
                  onClick={() => setEndDate('')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-brand-dark hover:bg-brand-secondary hover:text-white"
                  disabled={loading}
                >
                  ×
                </button>
              </span>
            )}
            {unitId && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand-dark">
                Unit: {units.find(u => u.id === unitId)?.name}
                <button
                  onClick={() => setUnitId('')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-brand-dark hover:bg-brand-secondary hover:text-white"
                  disabled={loading}
                >
                  ×
                </button>
              </span>
            )}
            {doctorId && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand-dark">
                Doctor: {doctors.find(d => d.id === doctorId)?.name}
                <button
                  onClick={() => setDoctorId('')}
                  className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-brand-dark hover:bg-brand-secondary hover:text-white"
                  disabled={loading}
                >
                  ×
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ClinicalFilters;
