// API Configuration
export const API_CONFIG = {
  // Backend options
  NODEJS_BACKEND: 'http://localhost:3001/api/clinical',
  FASTAPI_BACKEND: 'http://localhost:8000/api/clinical',
  
  // Current backend selection
  // Change this to switch between backends
  CURRENT_BACKEND: 'FASTAPI_BACKEND' as 'NODEJS_BACKEND' | 'FASTAPI_BACKEND',
  
  // Get the current API base URL
  get baseUrl() {
    return this[this.CURRENT_BACKEND];
  },
  
  // Switch backend
  switchBackend(backend: 'NODEJS_BACKEND' | 'FASTAPI_BACKEND') {
    this.CURRENT_BACKEND = backend;
  }
};

// Export the current base URL
export const API_BASE_URL = API_CONFIG.baseUrl;
