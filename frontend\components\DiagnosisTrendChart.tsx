
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cartes<PERSON>G<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
  }[];
}

interface DiagnosisTrendChartProps {
  data: ChartData;
}

const colors = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444'];

const DiagnosisTrendChart: React.FC<DiagnosisTrendChartProps> = ({ data }) => {
  const chartData = data.labels.map((label, index) => {
    const entry: { name: string; [key: string]: string | number } = { name: label };
    data.datasets.forEach(dataset => {
      entry[dataset.label] = dataset.data[index];
    });
    return entry;
  });

  return (
    <div style={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
            <LineChart
                data={chartData}
                margin={{
                    top: 5,
                    right: 20,
                    left: -10,
                    bottom: 5,
                }}
            >
                <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                <XAxis dataKey="name" stroke="#6b7280" fontSize={12} />
                <YAxis stroke="#6b7280" fontSize={12}/>
                <Tooltip 
                    contentStyle={{ 
                        backgroundColor: '#ffffff', 
                        border: '1px solid #e0e0e0',
                        borderRadius: '0.5rem'
                    }} 
                />
                <Legend iconSize={10} wrapperStyle={{fontSize: "12px"}}/>
                {data.datasets.map((dataset, index) => (
                    <Line 
                        key={dataset.label} 
                        type="monotone" 
                        dataKey={dataset.label} 
                        stroke={colors[index % colors.length]} 
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                    />
                ))}
            </LineChart>
        </ResponsiveContainer>
    </div>
  );
};

export default DiagnosisTrendChart;
