// Database row interfaces based on the SQL query structure
export interface ClinicalDataRow {
  visitid: string;
  opdipd: number;
  patientid: string;
  unitid: string;
  chiefcomplaint: string;
  diagnoses: string;
  gender: 'Male' | 'Female' | 'Other';
  age_group: '<16' | '17-25' | '26-40' | '41-55' | '56-65' | '>65';
  doctor_id: string;
  doctorname: string;
  medications: string | null;
  investigations: string | null;
}

// Frontend compatible interfaces
export interface Visit {
  id: string;
  patient: {
    id: string;
    age: number;
    gender: 'Male' | 'Female' | 'Other';
  };
  doctor: {
    id: string;
    name: string;
  };
  date: string; // ISO string
  chiefComplaint: string;
  diagnosis: string;
  medications: string[];
  investigations: string[];
  referred: boolean;
}

export interface Doctor {
  id: string;
  name: string;
}

export interface SymptomCluster {
  clusterName: string;
  symptoms: string[];
  probableDiagnosis: string;
}

// API Response interfaces
export interface ClinicalDataResponse {
  visits: Visit[];
  doctors: Doctor[];
  totalCount: number;
  page: number;
  limit: number;
}

export interface StatisticsResponse {
  totalPatients: number;
  totalVisits: number;
  totalDoctors: number;
  referralRate: number;
  polypharmacyCount: number;
  topComplaint: {
    name: string;
    count: number;
  };
  topDiagnosis: {
    name: string;
    count: number;
  };
  topMedication: {
    name: string;
    count: number;
  };
  avgInvestigations: string;
  mostFrequentPair: string;
}

export interface DiagnosisTrendData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
  }>;
}

export interface DoctorActivityData {
  name: string;
  avgPatients: string;
  topDiagnosis: string;
  avgMeds: string;
}

export interface DiagnosisByAgeData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
  }>;
}

// Query parameters interfaces
export interface ClinicalDataQuery {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  doctorId?: string;
  diagnosis?: string;
  ageGroup?: string;
  gender?: string;
}

export interface StatisticsQuery {
  startDate?: string;
  endDate?: string;
  doctorId?: string;
}

// Age group mapping
export const AGE_GROUP_RANGES: Record<string, { min: number; max: number }> = {
  '<16': { min: 0, max: 15 },
  '17-25': { min: 17, max: 25 },
  '26-40': { min: 26, max: 40 },
  '41-55': { min: 41, max: 55 },
  '56-65': { min: 56, max: 65 },
  '>65': { min: 66, max: 150 }
};

// Helper function to convert age group to actual age
export const getAgeFromAgeGroup = (ageGroup: string): number => {
  const range = AGE_GROUP_RANGES[ageGroup];
  if (!range) return 30; // Default age
  
  // Return middle of the range
  return Math.floor((range.min + range.max) / 2);
};
