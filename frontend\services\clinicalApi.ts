import { Visit, Doctor, SymptomCluster } from '../types';

const API_BASE_URL = 'http://localhost:3001/api/clinical';

// API Response interfaces
interface ApiResponse<T> {
  success: boolean;
  data: T;
  timestamp: string;
}

interface ClinicalDataResponse {
  visits: Visit[];
  doctors: Doctor[];
  totalCount: number;
  page: number;
  limit: number;
}

interface StatisticsResponse {
  totalPatients: number;
  totalVisits: number;
  totalDoctors: number;
  referralRate: number;
  polypharmacyCount: number;
  topComplaint: {
    name: string;
    count: number;
  };
  topDiagnosis: {
    name: string;
    count: number;
  };
  topMedication: {
    name: string;
    count: number;
  };
  avgInvestigations: string;
  mostFrequentPair: string;
}

interface DiagnosisTrendData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
  }>;
}

interface DoctorActivityData {
  name: string;
  avgPatients: string;
  topDiagnosis: string;
  avgMeds: string;
}

interface DiagnosisByAgeData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
  }>;
}

interface PatientDemographics {
  totalPatients: number;
  ageGroups: Record<string, number>;
  genderDistribution: Record<string, number>;
}

interface QualityMetrics {
  totalPatients: number;
  referralRate: number;
  polypharmacyCount: number;
  avgMedicationsPerVisit: number;
  avgInvestigationsPerVisit: number;
  mostCommonDiagnosis: string;
  mostCommonComplaint: string;
}

// Query parameters
interface ClinicalDataQuery {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  doctorId?: string;
  diagnosis?: string;
  ageGroup?: string;
  gender?: string;
  unitId?: string;
}

interface StatisticsQuery {
  startDate?: string;
  endDate?: string;
  doctorId?: string;
  unitId?: string;
}

class ClinicalApiService {
  private async fetchApi<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<T> = await response.json();
      
      if (!result.success) {
        throw new Error('API request failed');
      }

      return result.data;
    } catch (error) {
      console.error(`API Error for ${endpoint}:`, error);
      throw error;
    }
  }

  async getClinicalData(query: ClinicalDataQuery = {}): Promise<ClinicalDataResponse> {
    return this.fetchApi<ClinicalDataResponse>('/data', query);
  }

  async getStatistics(query: StatisticsQuery = {}): Promise<StatisticsResponse> {
    return this.fetchApi<StatisticsResponse>('/statistics', query);
  }

  async getDiagnosisTrends(query: StatisticsQuery = {}): Promise<DiagnosisTrendData> {
    return this.fetchApi<DiagnosisTrendData>('/diagnosis-trends', query);
  }

  async getDoctorActivity(query: StatisticsQuery = {}): Promise<DoctorActivityData[]> {
    return this.fetchApi<DoctorActivityData[]>('/doctor-activity', query);
  }

  async getDiagnosisByAge(query: StatisticsQuery = {}): Promise<DiagnosisByAgeData> {
    return this.fetchApi<DiagnosisByAgeData>('/diagnosis-by-age', query);
  }

  async getDoctors(): Promise<Doctor[]> {
    return this.fetchApi<Doctor[]>('/doctors');
  }

  async getUnits(): Promise<Array<{ id: string; name: string }>> {
    return this.fetchApi<Array<{ id: string; name: string }>>('/units');
  }

  async getSymptomClusters(): Promise<SymptomCluster[]> {
    return this.fetchApi<SymptomCluster[]>('/symptom-clusters');
  }

  async getPatientDemographics(): Promise<PatientDemographics> {
    return this.fetchApi<PatientDemographics>('/patient-demographics');
  }

  async getQualityMetrics(): Promise<QualityMetrics> {
    return this.fetchApi<QualityMetrics>('/quality-metrics');
  }

  async getHealthCheck(): Promise<any> {
    return this.fetchApi<any>('/health');
  }
}

export const clinicalApi = new ClinicalApiService();

// Export types for use in components
export type {
  ClinicalDataResponse,
  StatisticsResponse,
  DiagnosisTrendData,
  DoctorActivityData,
  DiagnosisByAgeData,
  PatientDemographics,
  QualityMetrics,
  ClinicalDataQuery,
  StatisticsQuery
};
