{"hospitals": [{"id": "hosp1", "name": "City General Hospital", "address": "123 Medical Center Drive", "phone": "******-0123", "departments": ["Internal Medicine", "Cardiology", "Emergency", "Surgery"], "bedCapacity": 500, "established": "1985"}, {"id": "hosp2", "name": "Regional Medical Center", "address": "456 Healthcare Blvd", "phone": "******-0456", "departments": ["Neurology", "Oncology", "Pediatrics", "Orthopedics"], "bedCapacity": 350, "established": "1992"}], "medicalConditions": [{"icd10": "J06.9", "name": "Acute upper respiratory infection, unspecified", "category": "Respiratory", "severity": "Mild", "commonSymptoms": ["<PERSON><PERSON>", "Sore throat", "Runny nose", "Fever"], "averageDuration": "7-10 days", "prevalence": "Very Common"}, {"icd10": "I10", "name": "Essential hypertension", "category": "Cardiovascular", "severity": "Moderate", "commonSymptoms": ["Headache", "Dizziness", "Often asymptomatic"], "averageDuration": "Chronic", "prevalence": "Common"}, {"icd10": "E11.9", "name": "Type 2 diabetes mellitus without complications", "category": "Endocrine", "severity": "Moderate", "commonSymptoms": ["Increased thirst", "Frequent urination", "Fatigue", "Blurred vision"], "averageDuration": "Chronic", "prevalence": "Common"}], "medications": [{"name": "Paracetamol", "genericName": "Acetaminophen", "category": "Analgesic/Antipyretic", "dosageForm": "Tablet", "strength": "500mg", "route": "Oral", "frequency": "Every 6-8 hours", "maxDailyDose": "4000mg", "contraindications": ["Severe liver disease"], "sideEffects": ["Rare: liver toxicity with overdose"]}, {"name": "Lisinopril", "genericName": "Lisinopril", "category": "ACE Inhibitor", "dosageForm": "Tablet", "strength": "10mg", "route": "Oral", "frequency": "Once daily", "maxDailyDose": "40mg", "contraindications": ["Pregnancy", "Angioedema history"], "sideEffects": ["Dry cough", "Hyperkalemia", "Hypotension"]}, {"name": "Metformin", "genericName": "Metformin HCl", "category": "Antidiabetic", "dosageForm": "Tablet", "strength": "500mg", "route": "Oral", "frequency": "Twice daily with meals", "maxDailyDose": "2000mg", "contraindications": ["Severe kidney disease", "Metabolic acidosis"], "sideEffects": ["<PERSON><PERSON><PERSON>", "Diarrhea", "Lactic acidosis (rare)"]}], "labTests": [{"code": "CBC", "name": "Complete Blood Count", "category": "Hematology", "specimen": "Blood", "normalRanges": {"WBC": "4.5-11.0 x10³/μL", "RBC": "4.5-5.5 x10⁶/μL", "Hemoglobin": "12-16 g/dL (F), 14-18 g/dL (M)", "Hematocrit": "36-46% (F), 41-53% (M)", "Platelets": "150-450 x10³/μL"}, "turnaroundTime": "2-4 hours", "cost": 25.0}, {"code": "BMP", "name": "Basic Metabolic Panel", "category": "Chemistry", "specimen": "Blood", "normalRanges": {"Glucose": "70-100 mg/dL", "BUN": "7-20 mg/dL", "Creatinine": "0.6-1.2 mg/dL", "Sodium": "136-145 mEq/L", "Potassium": "3.5-5.0 mEq/L", "Chloride": "98-107 mEq/L", "CO2": "22-28 mEq/L"}, "turnaroundTime": "1-2 hours", "cost": 35.0}], "vitalSigns": [{"parameter": "Blood Pressure", "unit": "mmHg", "normalRange": "90/60 - 120/80", "measurement": "Systolic/Diastolic"}, {"parameter": "Heart Rate", "unit": "bpm", "normalRange": "60-100", "measurement": "Beats per minute"}, {"parameter": "Temperature", "unit": "°F", "normalRange": "97.8-99.1", "measurement": "Oral temperature"}, {"parameter": "Respiratory Rate", "unit": "breaths/min", "normalRange": "12-20", "measurement": "Breaths per minute"}, {"parameter": "Oxygen Saturation", "unit": "%", "normalRange": "95-100", "measurement": "SpO2"}], "emergencyContacts": [{"service": "Emergency Medical Services", "phone": "911", "description": "Life-threatening emergencies"}, {"service": "Poison Control", "phone": "1-************", "description": "Poisoning emergencies"}, {"service": "Mental Health Crisis", "phone": "988", "description": "Suicide & Crisis Lifeline"}], "insuranceProviders": [{"id": "ins1", "name": "HealthFirst Insurance", "type": "HMO", "coverage": "Comprehensive", "copay": 25, "deductible": 1000}, {"id": "ins2", "name": "MediCare Plus", "type": "PPO", "coverage": "Premium", "copay": 15, "deductible": 500}, {"id": "ins3", "name": "Basic Health Plan", "type": "EPO", "coverage": "Basic", "copay": 40, "deductible": 2500}], "appointmentTypes": [{"type": "<PERSON><PERSON><PERSON>", "duration": 30, "cost": 150, "description": "Annual physical examination"}, {"type": "Follow-up V<PERSON>t", "duration": 15, "cost": 100, "description": "Follow-up for existing condition"}, {"type": "Consultation", "duration": 45, "cost": 200, "description": "Specialist consultation"}, {"type": "Emergency Visit", "duration": 60, "cost": 500, "description": "Urgent medical attention"}]}