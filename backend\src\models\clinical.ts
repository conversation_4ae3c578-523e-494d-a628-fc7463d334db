import { db } from '../utils/database.js';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Doctor, 
  ClinicalData<PERSON><PERSON><PERSON>,
  getAgeFromAgeGroup 
} from '../types/clinical.js';
import { logger } from '../utils/logger.js';

export class ClinicalModel {
  
  // Base query from the provided SQL
  private static readonly BASE_QUERY = `
    with cteopdclinicA as (
      select vpc.*,vpd.diagnoses from vw_patcomplaints vpc
      inner join vw_patdiagnosis vpd on 
      vpc.visitid = vpd.visitid 
      and vpc.opdipd = vpd.opdipd
      and vpc.patientid = vpd.patientid 
      and vpc.unitid = vpd.unitid
      where vpc.opdipd = 0
    ) 
    select A.*,dp.gender,
    CASE
      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) < 16::double precision THEN '<16'::text
      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 17::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 25::double precision THEN '17-25'::text
      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 26::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 40::double precision THEN '26-40'::text
      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 41::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 55::double precision THEN '41-55'::text
      WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 56::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 65::double precision THEN '56-65'::text
      ELSE '>65'::text
    END AS age_group,
    dd.doctor_id,
    'Dr. ' || dd.firstname || ' ' || dd.lastname as Doctorname,
    null as medications,
    null as investigations
    from cteopdclinicA A
    inner join dim_encounter de on A.visitid = de.encounterid
    inner join dim_patient dp on dp.patient_id = A.patientid 
    inner join dim_doctor dd on dd.doctor_id = de.doctormodalityid
    
  `;

  static async getClinicalData(query: ClinicalDataQuery = {}): Promise<{ data: Visit[], total: number }> {
    try {
      const {
        page = 1,
        limit = 100,
        startDate,
        endDate,
        doctorId,
        diagnosis,
        ageGroup,
        gender,
        unitId
      } = query;

      let whereConditions: string[] = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      // Add date filters using vpc.addeddatetime column
      if (startDate) {
        whereConditions.push(`A.addeddatetime >= $${paramIndex}`);
        queryParams.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        whereConditions.push(`A.addeddatetime <= $${paramIndex}`);
        queryParams.push(endDate);
        paramIndex++;
      }

      // Add unit filter
      if (unitId) {
        whereConditions.push(`A.unitid = $${paramIndex}`);
        queryParams.push(unitId);
        paramIndex++;
      }

      // Add doctor filter
      if (doctorId) {
        whereConditions.push(`dd.doctor_id = $${paramIndex}`);
        queryParams.push(doctorId);
        paramIndex++;
      }

      // Add diagnosis filter
      if (diagnosis) {
        whereConditions.push(`A.diagnoses ILIKE $${paramIndex}`);
        queryParams.push(`%${diagnosis}%`);
        paramIndex++;
      }

      // Add age group filter
      if (ageGroup) {
        whereConditions.push(`
          CASE
            WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) < 16::double precision THEN '<16'::text
            WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 17::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 25::double precision THEN '17-25'::text
            WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 26::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 40::double precision THEN '26-40'::text
            WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 41::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 55::double precision THEN '41-55'::text
            WHEN (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) >= 56::double precision AND (date_part('year'::text, CURRENT_DATE) - date_part('year'::text, dp.dateofbirth::date)) <= 65::double precision THEN '56-65'::text
            ELSE '>65'::text
          END = $${paramIndex}
        `);
        queryParams.push(ageGroup);
        paramIndex++;
      }

      // Add gender filter
      if (gender) {
        whereConditions.push(`dp.gender = $${paramIndex}`);
        queryParams.push(gender);
        paramIndex++;
      }

      // Build the complete query
      let fullQuery = this.BASE_QUERY;
      if (whereConditions.length > 0) {
        fullQuery += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      // Add ordering and pagination
      fullQuery += ` ORDER BY A.visitid DESC`;

      console.log(fullQuery);
      
      // Count query for total
      const countQuery = `SELECT COUNT(*) as total FROM (${fullQuery}) as counted_data`;
      const countResult = await db.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0]?.total || '0');

      // Add pagination to main query
      const offset = (page - 1) * limit;
      fullQuery += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      queryParams.push(limit, offset);

      const result = await db.query<ClinicalDataRow>(fullQuery, queryParams);
      
      // Transform database rows to frontend format
      const visits: Visit[] = result.rows.map(row => this.transformRowToVisit(row));

      

      return { data: visits, total };
    } catch (error) {
      logger.error('Error fetching clinical data', { error });
      throw error;
    }
  }

  static async getDoctors(): Promise<Doctor[]> {
    try {
      const query = `
        SELECT DISTINCT dd.doctor_id as id, 'Dr. ' || dd.firstname || ' ' || dd.lastname as name
        FROM dim_doctor dd
        INNER JOIN dim_encounter de ON dd.doctor_id = de.doctormodalityid
        ORDER BY name
      `;

      const result = await db.query<Doctor>(query);
      return result.rows;
    } catch (error) {
      logger.error('Error fetching doctors', { error });
      throw error;
    }
  }

  static async getUnits(): Promise<Array<{ id: string; name: string }>> {
    try {
      const query = `
        SELECT DISTINCT
          unitid as id,
          CASE
            WHEN unitid = '1' THEN 'Emergency Department'
            WHEN unitid = '2' THEN 'Internal Medicine'
            WHEN unitid = '3' THEN 'Cardiology'
            WHEN unitid = '4' THEN 'Pediatrics'
            WHEN unitid = '5' THEN 'Surgery'
            WHEN unitid = '6' THEN 'Orthopedics'
            WHEN unitid = '7' THEN 'Neurology'
            WHEN unitid = '8' THEN 'Oncology'
            ELSE 'Unit ' || unitid
          END as name
        FROM vw_patcomplaints
        WHERE unitid IS NOT NULL
          AND unitid != ''
          AND LENGTH(TRIM(unitid)) > 0
        ORDER BY unitid
      `;

      const result = await db.query<{ id: string; name: string }>(query);
      return result.rows;
    } catch (error) {
      logger.error('Error fetching units from database, returning mock data', { error });
      // Return mock data as fallback
      return [
        { id: '1', name: 'Emergency Department' },
        { id: '2', name: 'Internal Medicine' },
        { id: '3', name: 'Cardiology' },
        { id: '4', name: 'Pediatrics' },
        { id: '5', name: 'Surgery' },
        { id: '6', name: 'Orthopedics' },
        { id: '7', name: 'Neurology' },
        { id: '8', name: 'Oncology' }
      ];
    }
  }

  private static transformRowToVisit(row: ClinicalDataRow): Visit {
    // Generate a mock date since the database doesn't have a date column
    const mockDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);

    return {
      id: row.visitid,
      patient: {
        id: row.patientid,
        age: getAgeFromAgeGroup(row.age_group),
        gender: row.gender
      },
      doctor: {
        id: row.doctor_id,
        name: row.doctorname
      },
      date: mockDate.toISOString(),
      chiefComplaint: row.chiefcomplaint || '',
      diagnosis: row.diagnoses || '',
      medications: row.medications ? row.medications.split(',').map(m => m.trim()) : [],
      investigations: row.investigations ? row.investigations.split(',').map(i => i.trim()) : [],
      referred: Math.random() > 0.88 // 12% referral rate as per mock data
    };
  }
}
