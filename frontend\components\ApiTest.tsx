import React, { useState } from 'react';
import { clinicalApi } from '../services/clinicalApi';

const ApiTest: React.FC = () => {
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const testEndpoint = async (name: string, apiCall: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    try {
      const result = await apiCall();
      setTestResults(prev => ({ ...prev, [name]: { success: true, data: result } }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        } 
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const tests = [
    {
      name: 'Health Check',
      call: () => clinicalApi.getHealthCheck()
    },
    {
      name: 'Doctors',
      call: () => clinicalApi.getDoctors()
    },
    {
      name: 'Clinical Data (5 records)',
      call: () => clinicalApi.getClinicalData({ limit: 5 })
    },
    {
      name: 'Statistics',
      call: () => clinicalApi.getStatistics()
    },
    {
      name: 'Diagnosis Trends',
      call: () => clinicalApi.getDiagnosisTrends()
    },
    {
      name: 'Doctor Activity',
      call: () => clinicalApi.getDoctorActivity()
    },
    {
      name: 'Diagnosis by Age',
      call: () => clinicalApi.getDiagnosisByAge()
    },
    {
      name: 'Symptom Clusters',
      call: () => clinicalApi.getSymptomClusters()
    }
  ];

  const runAllTests = async () => {
    for (const test of tests) {
      await testEndpoint(test.name, test.call);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4">API Integration Test</h2>
      
      <div className="mb-4">
        <button
          onClick={runAllTests}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mr-2"
        >
          Run All Tests
        </button>
      </div>

      <div className="space-y-4">
        {tests.map((test) => (
          <div key={test.name} className="border rounded p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">{test.name}</h3>
              <button
                onClick={() => testEndpoint(test.name, test.call)}
                disabled={loading[test.name]}
                className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 disabled:bg-gray-400"
              >
                {loading[test.name] ? 'Testing...' : 'Test'}
              </button>
            </div>
            
            {testResults[test.name] && (
              <div className={`p-3 rounded text-sm ${
                testResults[test.name].success 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {testResults[test.name].success ? (
                  <div>
                    <div className="font-medium">✅ Success</div>
                    <pre className="mt-2 text-xs overflow-auto max-h-32">
                      {JSON.stringify(testResults[test.name].data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div>
                    <div className="font-medium">❌ Error</div>
                    <div className="mt-1">{testResults[test.name].error}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ApiTest;
